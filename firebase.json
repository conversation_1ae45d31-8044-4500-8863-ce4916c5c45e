{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["pnpm --prefix \"$RESOURCE_DIR\" run build"]}], "emulators": {"functions": {"port": 5003, "host": "0.0.0.0"}, "firestore": {"port": 8083, "host": "0.0.0.0"}, "database": {"port": 9003, "host": "0.0.0.0"}, "pubsub": {"port": 8087, "host": "0.0.0.0"}, "ui": {"enabled": true, "port": 4003, "host": "0.0.0.0"}, "hub": {"port": 4403, "host": "0.0.0.0"}, "logging": {"port": 4503, "host": "0.0.0.0"}, "singleProjectMode": true}}