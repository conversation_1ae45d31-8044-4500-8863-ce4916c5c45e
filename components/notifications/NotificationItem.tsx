import { MaterialIcons } from '@expo/vector-icons';
import React, { useCallback } from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Card, Chip, Text, useTheme } from 'react-native-paper';

import { Notification } from '@/types/notification';

interface NotificationItemProps {
  /** 通知數據 */
  notification: Notification;
  /** 點擊回調函數 */
  onPress?: (notification: Notification) => void;
}

/**
 * 通知項目組件
 * 顯示單個通知的完整信息，包括狀態指示器、內容和操作按鈕
 */
export function NotificationItem({ notification, onPress }: NotificationItemProps) {
  const theme = useTheme();

  // 格式化時間顯示
  const formatTimestamp = useCallback((timestamp: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
      return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return diffInHours === 1 ? '1 hour ago' : `${diffInHours} hours ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday, ' + timestamp.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} days ago, ` + timestamp.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
    }
  }, []);

  // 獲取狀態配置
  const getStatusConfig = useCallback(() => {
    if (notification.status === 'pending') {
      return {
        borderColor: theme.colors.tertiary, // 黃色邊框
        backgroundColor: theme.dark ? 'rgba(251, 191, 36, 0.1)' : '#FEF3C7', // 黃色背景
        chipColor: theme.dark ? '#F59E0B' : '#D97706', // 黃色 chip
        chipBackgroundColor: theme.dark ? 'rgba(251, 191, 36, 0.2)' : '#FEF3C7',
        icon: 'hourglass-empty' as const,
        statusText: 'Pending'
      };
    } else {
      return {
        borderColor: theme.colors.primary, // 綠色邊框
        backgroundColor: theme.dark ? 'rgba(34, 197, 94, 0.1)' : '#DCFCE7', // 綠色背景
        chipColor: theme.dark ? '#22C55E' : '#16A34A', // 綠色 chip
        chipBackgroundColor: theme.dark ? 'rgba(34, 197, 94, 0.2)' : '#DCFCE7',
        icon: 'check-circle-outline' as const,
        statusText: 'Confirmed'
      };
    }
  }, [notification.status, theme]);

  // 處理點擊事件
  const handlePress = useCallback(() => {
    // 只調用外部點擊回調，不自動確認通知
    // 確認操作將在詳情頁面中處理
    onPress?.(notification);
  }, [notification, onPress]);

  const statusConfig = getStatusConfig();

  return (
    <TouchableOpacity
      onPress={handlePress}
      activeOpacity={0.7}
      style={styles.container}
    >
      <Card 
        style={[
          styles.card,
          { 
            backgroundColor: statusConfig.backgroundColor,
            borderLeftColor: statusConfig.borderColor,
          }
        ]}
        elevation={1}
      >
        <Card.Content style={styles.cardContent}>
          <View style={styles.contentContainer}>
            {/* 左側內容區域 */}
            <View style={styles.leftContent}>
              <Text 
                style={[
                  styles.title,
                  { 
                    color: theme.colors.onSurface,
                    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                  }
                ]}
                numberOfLines={1}
              >
                {notification.title}
              </Text>
              
              <Text 
                style={[
                  styles.description,
                  { 
                    color: theme.colors.onSurfaceVariant,
                    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                  }
                ]}
                numberOfLines={2}
              >
                {notification.description}
              </Text>
              
              <Text 
                style={[
                  styles.timestamp,
                  { 
                    color: theme.colors.onSurfaceVariant,
                    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                  }
                ]}
              >
                {formatTimestamp(notification.timestamp)}
              </Text>
            </View>

            {/* 右側狀態區域 */}
            <View style={styles.rightContent}>
              <Chip
                icon={() => (
                  <MaterialIcons 
                    name={statusConfig.icon} 
                    size={16} 
                    color={statusConfig.chipColor} 
                  />
                )}
                style={[
                  styles.statusChip,
                  { backgroundColor: statusConfig.chipBackgroundColor }
                ]}
                textStyle={[
                  styles.statusText,
                  { color: statusConfig.chipColor }
                ]}
                compact
              >
                {statusConfig.statusText}
              </Chip>
              
              <MaterialIcons 
                name="chevron-right" 
                size={20} 
                color={theme.colors.onSurfaceVariant}
                style={styles.chevronIcon}
              />
            </View>
          </View>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  card: {
    borderLeftWidth: 4,
    borderRadius: 8,
    // backgroundColor 通過 statusConfig 動態設置
    // borderLeftColor 通過 statusConfig 動態設置
  },
  cardContent: {
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftContent: {
    flex: 1,
    marginRight: 12,
  },
  rightContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    // color 通過主題動態設置
  },
  description: {
    fontSize: 14,
    marginBottom: 6,
    lineHeight: 20,
    // color 通過主題動態設置
  },
  timestamp: {
    fontSize: 12,
    // color 通過主題動態設置
  },
  statusChip: {
    marginBottom: 8,
    // backgroundColor 通過 statusConfig 動態設置
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    // color 通過 statusConfig 動態設置
  },
  chevronIcon: {
    // color 通過主題動態設置
  },
});
