import * as Linking from 'expo-linking';
import React, { useRef } from 'react';
import { Animated, StyleSheet, View } from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { IconButton, useTheme } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';

interface SwipeToCallProps {
  /** 子組件 */
  children: React.ReactNode;
  /** 電話號碼 */
  phoneNumber?: string;
  /** 是否啟用滑動功能 */
  enabled?: boolean;
  /** 滑動回調函數 */
  onSwipe?: () => void;
}

/**
 * 滑動拨號組件
 * 支持向右滑動觸發拨打電話功能，滑動時左側平滑出現電話圖標
 */
export function SwipeToCall({
  children,
  phoneNumber,
  enabled = true,
  onSwipe
}: SwipeToCallProps) {
  const theme = useTheme();
  const translateX = useRef(new Animated.Value(0)).current;
  const iconOpacity = useRef(new Animated.Value(0)).current;
  const iconScale = useRef(new Animated.Value(0.5)).current;

  // 處理手勢狀態變化
  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { 
      useNativeDriver: false,
      listener: (event: any) => {
        const { translationX: tx } = event.nativeEvent;
        
        // 只允許向右滑動（正值）
        if (tx > 0) {
          // 根據滑動距離計算圖標透明度和縮放
          const progress = Math.min(tx / 80, 1);
          iconOpacity.setValue(progress);
          iconScale.setValue(0.5 + progress * 0.5);
        }
      }
    }
  );

  // 處理手勢結束
  const onHandlerStateChange = (event: any) => {
    if (!enabled) return;

    const { state, translationX: tx } = event.nativeEvent;

    if (state === State.END) {
      const threshold = 80; // 向右滑動的閾值
      
      if (tx > threshold) {
        // 觸發拨號操作
        handleCall();
        onSwipe?.();
        
        // 短暫保持滑動狀態然後重置
        setTimeout(() => {
          resetPosition();
        }, 300);
      } else {
        // 重置位置
        resetPosition();
      }
    }
  };

  // 重置位置和動畫
  const resetPosition = () => {
    Animated.parallel([
      Animated.timing(translateX, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(iconOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(iconScale, {
        toValue: 0.5,
        duration: 300,
        useNativeDriver: false,
      })
    ]).start();
  };

  // 處理電話拨打
  const handleCall = async () => {
    if (!phoneNumber) return;

    try {
      const phoneUrl = `tel:${phoneNumber}`;
      const canOpen = await Linking.canOpenURL(phoneUrl);
      
      if (canOpen) {
        await Linking.openURL(phoneUrl);
      } else {
        console.warn('無法打開電話應用');
      }
    } catch (error) {
      console.error('拨打電話時發生錯誤:', error);
    }
  };

  if (!enabled) {
    return <View style={styles.container}>{children}</View>;
  }

  return (
    <View style={styles.container}>
      {/* 左側滑動顯示的電話圖標背景 */}
      <View style={[
        styles.leftActionBackground,
        { backgroundColor: theme.colors.primary }
      ]}>
        <Animated.View
          style={[
            styles.leftActionContent,
            {
              opacity: iconOpacity,
              transform: [{ scale: iconScale }]
            }
          ]}
        >
          <MaterialIcons
            name="phone"
            size={28}
            color={theme.colors.onPrimary}
          />
        </Animated.View>
      </View>

      {/* 可滑動的內容 */}
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
        activeOffsetX={10} // 只允許向右滑動
        failOffsetY={[-5, 5]}
      >
        <Animated.View
          style={[
            styles.swipeContent,
            {
              transform: [{ 
                translateX: translateX.interpolate({
                  inputRange: [-50, 0, 100],
                  outputRange: [0, 0, 100],
                  extrapolate: 'clamp'
                })
              }],
              backgroundColor: theme.colors.surface,
            }
          ]}
        >
          {children}
        </Animated.View>
      </PanGestureHandler>

      {/* 滑動提示覆蓋層 */}
      <Animated.View
        style={[
          styles.swipeIndicator,
          {
            opacity: iconOpacity.interpolate({
              inputRange: [0, 0.3, 1],
              outputRange: [0, 0.1, 0.2],
              extrapolate: 'clamp'
            }),
            backgroundColor: theme.colors.primary,
          }
        ]}
        pointerEvents="none"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 8,
  },
  leftActionBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: 100,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  leftActionContent: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  swipeContent: {
    zIndex: 2,
    borderRadius: 8,
  },
  swipeIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 3,
    pointerEvents: 'none',
    borderRadius: 8,
  },
});
