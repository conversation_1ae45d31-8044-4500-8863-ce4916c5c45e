import { MaterialIcons } from '@expo/vector-icons';
import React, { useState, useCallback } from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text, useTheme } from 'react-native-paper';

import { Notification } from '@/types/notification';
import { NotificationItem } from './NotificationItem';

interface NotificationSectionProps {
  /** 分組標題 */
  title: string;
  /** 通知列表 */
  notifications: Notification[];
  /** 是否默認展開 */
  defaultExpanded?: boolean;
  /** 通知點擊回調 */
  onNotificationPress?: (notification: Notification) => void;
}

/**
 * 通知分組組件
 * 顯示一組相同狀態的通知，支持展開/收起功能
 */
export function NotificationSection({ 
  title, 
  notifications, 
  defaultExpanded = true,
  onNotificationPress 
}: NotificationSectionProps) {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(defaultExpanded);

  // 切換展開/收起狀態
  const toggleExpanded = useCallback(() => {
    setExpanded(prev => !prev);
  }, []);

  // 如果沒有通知，不渲染組件
  if (notifications.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* 分組標題區域 */}
      <TouchableOpacity
        style={styles.headerContainer}
        onPress={toggleExpanded}
        activeOpacity={0.7}
      >
        <View style={styles.headerContent}>
          <Text 
            style={[
              styles.sectionTitle,
              { 
                color: theme.colors.onSurface,
                fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
              }
            ]}
          >
            {title}
          </Text>
          
          {/* 通知數量標識 */}
          {notifications.length > 0 && (
            <View style={[
              styles.countBadge,
              { backgroundColor: theme.colors.primaryContainer }
            ]}>
              <Text style={[
                styles.countText,
                { color: theme.colors.primary }
              ]}>
                {notifications.length}
              </Text>
            </View>
          )}
        </View>

        {/* 展開/收起圖標 */}
        <MaterialIcons
          name={expanded ? 'expand-less' : 'expand-more'}
          size={24}
          color={theme.colors.onSurfaceVariant}
          style={[
            styles.expandIcon,
            { transform: [{ rotate: expanded ? '0deg' : '0deg' }] }
          ]}
        />
      </TouchableOpacity>

      {/* 通知列表區域 */}
      {expanded && (
        <View style={styles.notificationsContainer}>
          {notifications.map((notification, index) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onPress={onNotificationPress}
            />
          ))}
          
          {/* 底部提示信息 */}
          {notifications.length > 0 && (
            <Text style={[
              styles.footerText,
              { 
                color: theme.colors.onSurfaceVariant,
                fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
              }
            ]}>
              {notifications[0].status === 'pending' 
                ? 'Tap notifications to view details'
                : 'Notifications older than 24 hours are automatically cleared'
              }
            </Text>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700', // 粗體標題，符合用戶偏好
    marginRight: 12,
    // color 通過主題動態設置
  },
  countBadge: {
    minWidth: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
    // backgroundColor 通過主題動態設置
  },
  countText: {
    fontSize: 12,
    fontWeight: '600',
    // color 通過主題動態設置
  },
  expandIcon: {
    marginLeft: 8,
    // color 通過主題動態設置
  },
  notificationsContainer: {
    paddingHorizontal: 16,
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
    fontStyle: 'italic',
    opacity: 0.7,
    // color 通過主題動態設置
  },
});
