import { MaterialIcons } from '@expo/vector-icons';
import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Platform, ScrollView, StyleSheet, Text as RNText, TouchableOpacity, View } from 'react-native';
import {
  Appbar,
  Button,
  Divider,
  Modal,
  Portal,
  Surface,
  Text,
  TextInput,
  useTheme,
} from 'react-native-paper';
import { STAFF_DATA, STAFF_GROUPS, getGroupById, addGroup, updateGroup, deleteGroup } from '@/data/staff';

// 模式枚举
type EditGroupMode = 'list' | 'edit' | 'add';

// 记忆化的群组项目组件以提升性能
const MemoizedGroupItem = memo(({ 
  group, 
  onPress, 
  theme
}: {
  group: typeof STAFF_GROUPS[0];
  onPress: (group: typeof STAFF_GROUPS[0]) => void;
  theme: any;
}) => (
  <Surface
    style={[styles.groupItem, { backgroundColor: theme.colors.surfaceVariant }]}
    elevation={1}
  >
    <TouchableOpacity
      style={styles.groupItemTouchable}
      onPress={() => onPress(group)}
      activeOpacity={0.7}
    >
      <View style={styles.groupItemContent}>
        <View style={styles.groupItemLeft}>
          <View style={[styles.groupAvatar, { backgroundColor: group.color }]}>
            <MaterialIcons name={group.icon} size={20} color="#FFFFFF" />
          </View>
          <View style={styles.groupInfo}>
            <Text style={[styles.groupName, { color: theme.colors.onSurface }]}>
              {group.name}
            </Text>
            <Text style={[styles.groupMembers, { color: theme.colors.onSurfaceVariant }]}>
              {group.memberIds?.length || 0} members
            </Text>
          </View>
        </View>
        <MaterialIcons 
          name="chevron-right" 
          size={24} 
          color={theme.colors.onSurfaceVariant} 
        />
      </View>
    </TouchableOpacity>
  </Surface>
));

MemoizedGroupItem.displayName = 'MemoizedGroupItem';

// 記憶化的成員項目組件以提升性能 - 支持整栏点击
const MemoizedMemberItem = memo(({ 
  member, 
  onAction, 
  theme,
  actionType = 'remove'
}: {
  member: typeof STAFF_DATA[0];
  onAction: (id: string) => void;
  theme: any;
  actionType?: 'add' | 'remove';
}) => (
  <Surface
    style={[
      styles.memberItem,
      { backgroundColor: actionType === 'remove' ? theme.colors.surfaceVariant : theme.colors.surface }
    ]}
    elevation={1}
  >
    <TouchableOpacity
      style={styles.memberItemTouchable}
      onPress={() => onAction(member.id)}
      activeOpacity={0.7}
    >
      <View style={styles.memberContent}>
        <View style={styles.memberLeft}>
          <View style={[styles.memberAvatar, { backgroundColor: member.color }]}>
            <RNText style={styles.memberInitials}>{member.initials}</RNText>
          </View>
          <Text style={[styles.memberText, { color: theme.colors.onSurface }]}>
            {member.name}
          </Text>
        </View>
        <View style={styles.actionButton}>
          <MaterialIcons 
            name={actionType === 'remove' ? "remove-circle-outline" : "add-circle-outline"} 
            size={24} 
            color={actionType === 'remove' ? theme.colors.error : theme.colors.primary} 
          />
        </View>
      </View>
    </TouchableOpacity>
  </Surface>
));

MemoizedMemberItem.displayName = 'MemoizedMemberItem';

interface EditGroupModalProps {
  visible: boolean;
  onDismiss: () => void;
  onSave?: (groupData: { id?: string; name: string; memberIds: string[] }) => void;
  onDelete?: (groupId: string) => void;
}

// Edit Group modal component - 重构为支持群组列表和编辑/添加模式
export default function EditGroupModal({
  visible,
  onDismiss,
  onSave,
  onDelete,
}: EditGroupModalProps) {
  // Get current theme to support Dark Mode
  const theme = useTheme();
  
  // 模式状态：列表、编辑、添加
  const [mode, setMode] = useState<EditGroupMode>('list');
  const [selectedGroup, setSelectedGroup] = useState<typeof STAFF_GROUPS[0] | null>(null);
  const [refreshKey, setRefreshKey] = useState(0); // 用于触发列表刷新
  
  // Form state
  const [groupName, setGroupName] = useState('');
  const [memberIds, setMemberIds] = useState<string[]>([]);
  const [isClosing, setIsClosing] = useState(false);

  // 重置表单状态
  const resetFormState = useCallback(() => {
    setGroupName('');
    setMemberIds([]);
    setIsClosing(false);
  }, []);

  // 當 modal 顯示狀態改變時重置狀態
  useEffect(() => {
    if (visible) {
      setMode('list');
      setSelectedGroup(null);
      setIsClosing(false);
      setRefreshKey(prev => prev + 1); // 触发列表刷新
      resetFormState();
    }
  }, [visible, resetFormState]);

  // 处理群组选择（进入编辑模式）
  const handleGroupSelect = useCallback((group: typeof STAFF_GROUPS[0]) => {
    setSelectedGroup(group);
    setGroupName(group.name);
    setMemberIds([...(group.memberIds || [])]);
    setMode('edit');
  }, []);

  // 处理添加新群组（进入添加模式）
  const handleAddGroup = useCallback(() => {
    setSelectedGroup(null);
    resetFormState();
    setMode('add');
  }, [resetFormState]);

  // 返回到列表视图
  const handleBackToList = useCallback(() => {
    setMode('list');
    setSelectedGroup(null);
    resetFormState();
  }, [resetFormState]);

  // Get current members and available members - 使用 useMemo 優化性能
  const currentMembers = useMemo(() => 
    STAFF_DATA.filter(staff => memberIds.includes(staff.id)), 
    [memberIds]
  );
  
  const availableMembers = useMemo(() => 
    STAFF_DATA.filter(staff => !memberIds.includes(staff.id)), 
    [memberIds]
  );

  // Handle adding member to group
  const handleAddMember = useCallback((staffId: string) => {
    if (!isClosing) {
      setMemberIds(prev => [...prev, staffId]);
    }
  }, [isClosing]);

  // Handle removing member from group
  const handleRemoveMember = useCallback((staffId: string) => {
    if (!isClosing) {
      setMemberIds(prev => prev.filter(id => id !== staffId));
    }
  }, [isClosing]);

  // 優化的關閉處理函數
  const handleDismiss = useCallback(() => {
    if (!isClosing) {
      setIsClosing(true);
      requestAnimationFrame(() => {
        onDismiss();
      });
    }
  }, [isClosing, onDismiss]);

  // Handle save - 支持编辑和添加模式，实际更新数据
  const handleSave = useCallback(() => {
    if (!isClosing && groupName.trim()) {
      setIsClosing(true);

      try {
        if (mode === 'edit' && selectedGroup) {
          // 更新现有群组
          const success = updateGroup(selectedGroup.id, { name: groupName, memberIds });
          if (success) {
            setRefreshKey(prev => prev + 1); // 触发列表刷新
            if (onSave) {
              onSave({ 
                id: selectedGroup.id,
                name: groupName, 
                memberIds 
              });
            }
          }
        } else if (mode === 'add') {
          // 添加新群组
          const newGroup = addGroup({ name: groupName, memberIds });
          setRefreshKey(prev => prev + 1); // 触发列表刷新
          if (onSave) {
            onSave({ 
              id: newGroup.id,
              name: groupName, 
              memberIds 
            });
          }
        }
      } catch (error) {
        console.error('Error saving group:', error);
      }

      requestAnimationFrame(() => {
        onDismiss();
      });
    }
  }, [isClosing, groupName, memberIds, mode, selectedGroup, onSave, onDismiss]);

  // Handle delete - 只在编辑模式下可用，实际删除数据
  const handleDelete = useCallback(() => {
    if (!isClosing && mode === 'edit' && selectedGroup) {
      setIsClosing(true);

      try {
        // 实际删除群组数据
        const success = deleteGroup(selectedGroup.id);
        if (success) {
          setRefreshKey(prev => prev + 1); // 触发列表刷新
          if (onDelete) {
            onDelete(selectedGroup.id);
          }
        }
      } catch (error) {
        console.error('Error deleting group:', error);
      }

      requestAnimationFrame(() => {
        onDismiss();
      });
    }
  }, [isClosing, mode, selectedGroup, onDelete, onDismiss]);

  // 渲染群组列表视图
  const renderGroupsList = () => (
    <>
      {/* Header */}
      <Appbar.Header 
        style={[styles.header, { backgroundColor: theme.colors.surface }]}
        statusBarHeight={0}
      >
        <Appbar.BackAction onPress={handleDismiss} />
        <Appbar.Content
          title="Manage Groups"
          titleStyle={[styles.headerTitle, { color: theme.colors.onSurface }]}
        />
      </Appbar.Header>

      {/* Content */}
             <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
         <View style={styles.section}>
           <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
             Groups ({STAFF_GROUPS.length})
           </Text>
           {STAFF_GROUPS.map((group) => (
             <MemoizedGroupItem
               key={`${group.id}-${refreshKey}`} // 使用 refreshKey 确保重新渲染
               group={group}
               onPress={handleGroupSelect}
               theme={theme}
             />
           ))}
         </View>
       </ScrollView>

      {/* Add Group Button */}
      <View style={styles.bottomContainer}>
        <Divider style={{ backgroundColor: theme.colors.outline }} />
        <Button
          mode="contained"
          onPress={handleAddGroup}
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          contentStyle={styles.addButtonContent}
          labelStyle={[styles.addButtonLabel, { color: theme.colors.onPrimary }]}
          disabled={isClosing}
          icon={({ size }) => (
            <MaterialIcons name="add" size={size} color={theme.colors.onPrimary} />
          )}
        >
          Add Group
        </Button>
      </View>
    </>
  );

  // 渲染编辑/添加表单视图
  const renderEditForm = () => (
    <>
      {/* Header */}
      <Appbar.Header 
        style={[styles.header, { backgroundColor: theme.colors.surface }]}
        statusBarHeight={0}
      >
        <Appbar.BackAction onPress={handleBackToList} />
        <Appbar.Content
          title={mode === 'edit' ? 'Edit Group' : 'Add Group'}
          titleStyle={[styles.headerTitle, { color: theme.colors.onSurface }]}
        />
      </Appbar.Header>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Group Name Input */}
        <View style={styles.section}>
          <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>
            Group Name
          </Text>
          <TextInput
            value={groupName}
            onChangeText={setGroupName}
            style={[styles.groupNameInput, { backgroundColor: theme.colors.surface }]}
            mode="outlined"
            placeholder="Enter group name"
            dense
          />
        </View>

        {/* Current Members Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Members ({currentMembers.length})
            </Text>
          </View>
          {currentMembers.length === 0 ? (
            <Text style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
              No members yet. Add members from the list below.
            </Text>
          ) : (
            currentMembers.map((member) => (
              <MemoizedMemberItem
                key={member.id}
                member={member}
                onAction={handleRemoveMember}
                theme={theme}
                actionType="remove"
              />
            ))
          )}
        </View>

        {/* Available Members Section */}
        {availableMembers.length > 0 && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Other Contacts ({availableMembers.length})
            </Text>
            {availableMembers.map((member) => (
              <MemoizedMemberItem
                key={member.id}
                member={member}
                onAction={handleAddMember}
                theme={theme}
                actionType="add"
              />
            ))}
          </View>
        )}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomContainer}>
        <Divider style={{ backgroundColor: theme.colors.outline }} />
        
        {/* Delete Button - 只在编辑模式下显示 */}
        {mode === 'edit' && (
          <Button
            mode="outlined"
            onPress={handleDelete}
            style={[
              styles.deleteButton,
              {
                borderColor: theme.colors.error,
                backgroundColor: theme.dark ? 'rgba(244, 67, 54, 0.1)' : '#FFEBEE'
              }
            ]}
            contentStyle={styles.deleteButtonContent}
            labelStyle={[styles.deleteButtonLabel, { color: theme.colors.error }]}
            disabled={isClosing}
            icon={({ size }) => (
              <MaterialIcons name="delete-outline" size={size} color={theme.colors.error} />
            )}
          >
            Delete Group
          </Button>
        )}

        {/* Save Button */}
        <Button
          mode="contained"
          onPress={handleSave}
          style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
          contentStyle={styles.saveButtonContent}
          labelStyle={[styles.saveButtonLabel, { color: theme.colors.onPrimary }]}
          disabled={!groupName.trim() || isClosing}
        >
          {mode === 'edit' ? 'Save' : 'Create'}
        </Button>
      </View>
    </>
  );

  return (
    <Portal>
      <Modal
        visible={visible && !isClosing}
        onDismiss={handleDismiss}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
        dismissable={!isClosing}
        dismissableBackButton={!isClosing}
      >
        {mode === 'list' ? renderGroupsList() : renderEditForm()}
      </Modal>
    </Portal>
  );
}

// Style definitions - using theme colors to support Dark Mode and platform-specific fonts
const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    margin: 0,
  },
  header: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    height: 64,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  groupNameInput: {
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  // 群组列表项样式
  groupItem: {
    borderRadius: 12,
    marginBottom: 8,
    padding: 12,
  },
  groupItemTouchable: {
    flex: 1,
  },
  groupItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  groupItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  groupAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  groupMembers: {
    fontSize: 14,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    marginTop: 2,
  },
  // 成员项样式
  memberItem: {
    borderRadius: 12,
    marginBottom: 8,
    padding: 12,
  },
  memberItemTouchable: {
    flex: 1,
  },
  memberContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  memberLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  memberInitials: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  memberText: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  actionButton: {
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 16,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  bottomContainer: {
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16,
  },
  addButton: {
    borderRadius: 12,
    marginTop: 12,
  },
  addButtonContent: {
    paddingVertical: 8,
  },
  addButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  deleteButton: {
    borderRadius: 12,
    marginTop: 12,
    marginBottom: 8,
  },
  deleteButtonContent: {
    paddingVertical: 8,
  },
  deleteButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  saveButton: {
    borderRadius: 12,
    marginTop: 8,
  },
  saveButtonContent: {
    paddingVertical: 8,
  },
  saveButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
});
