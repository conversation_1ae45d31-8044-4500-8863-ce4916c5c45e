#!/usr/bin/env node

/**
 * Firebase 環境配置和 Cloud Functions 測試腳本
 * 
 * 測試內容：
 * 1. 成功的用戶註冊測試
 * 2. 參數驗證測試
 * 3. 通知創建測試
 * 4. 環境隔離驗證
 * 
 * 使用方法：
 * pnpm run test-firebase-environment
 */

import { httpsCallable } from 'firebase/functions';
import { functions, getEnvironmentInfo, isDevelopment } from '../utils/firebaseEnvironment';

// 模擬數據常量
const MOCK_DEVICE_ID = `test_device_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
const MOCK_FCM_TOKEN = `test_fcm_token_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
const MOCK_EVENT_ID = `test_event_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

// 測試用戶數據
const MOCK_USER_DATA = {
  deviceID: MOCK_DEVICE_ID,
  nickname: '測試用戶',
  fcmToken: MOCK_FCM_TOKEN,
  name: 'Test User',
  role: 'nurse',
  initials: 'TU',
  color: '#FF5722',
  phoneNumber: '+886912345678'
};

// 測試通知數據
const MOCK_ALERT_DATA = {
  initiatorDeviceID: MOCK_DEVICE_ID,
  caseType: 'emergency',
  motherInitial: 'A',
  bedNumber: '101',
  designatedWard: 'ICU',
  clinicalNotes: '測試緊急通知',
  recipientDeviceIDs: [MOCK_DEVICE_ID]
};

/**
 * 測試結果統計
 */
interface TestResult {
  name: string;
  success: boolean;
  message: string;
  duration: number;
}

const testResults: TestResult[] = [];

/**
 * 執行測試並記錄結果
 */
async function runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
  const startTime = Date.now();
  console.log(`\n🧪 開始測試: ${testName}`);
  
  try {
    await testFn();
    const duration = Date.now() - startTime;
    testResults.push({
      name: testName,
      success: true,
      message: '測試通過',
      duration
    });
    console.log(`✅ ${testName} - 測試通過 (${duration}ms)`);
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : '未知錯誤';
    testResults.push({
      name: testName,
      success: false,
      message: errorMessage,
      duration
    });
    console.error(`❌ ${testName} - 測試失敗: ${errorMessage} (${duration}ms)`);
  }
}

/**
 * 測試1: 環境隔離驗證
 */
async function testEnvironmentIsolation(): Promise<void> {
  const envInfo = getEnvironmentInfo();
  
  console.log('📊 環境信息:', JSON.stringify(envInfo, null, 2));
  
  // 驗證環境配置
  if (isDevelopment) {
    if (envInfo.firebaseProject !== 'qmnoti-test') {
      throw new Error(`開發環境應使用 qmnoti-test 項目，實際: ${envInfo.firebaseProject}`);
    }
    console.log('✅ 開發環境配置正確 - 使用測試項目');
  } else {
    if (envInfo.firebaseProject !== 'qmnoti') {
      throw new Error(`生產環境應使用 qmnoti 項目，實際: ${envInfo.firebaseProject}`);
    }
    console.log('✅ 生產環境配置正確 - 使用正式項目');
  }
  
  // 驗證集合名稱配置
  const collections = envInfo.collections;
  if (!collections.firestore.users || !collections.firestore.alertEvents) {
    throw new Error('集合名稱配置不完整');
  }
  
  console.log('✅ 集合名稱配置正確');
}

/**
 * 測試2: 健康檢查
 */
async function testHealthCheck(): Promise<void> {
  const healthCheck = httpsCallable(functions, 'healthCheck');
  
  const result = await healthCheck();
  
  if (!result.data || typeof result.data !== 'object') {
    throw new Error('健康檢查返回數據格式錯誤');
  }
  
  console.log('✅ Firebase Functions 健康檢查通過');
  console.log('📋 健康檢查結果:', result.data);
}

/**
 * 測試3: 成功的用戶註冊測試
 */
async function testSuccessfulUserRegistration(): Promise<void> {
  const registerUser = httpsCallable(functions, 'registerUser');
  
  const result = await registerUser(MOCK_USER_DATA);
  
  if (!result.data || !result.data.success) {
    throw new Error(`用戶註冊失敗: ${result.data?.message || '未知錯誤'}`);
  }
  
  console.log('✅ 用戶註冊成功');
  console.log('📋 註冊結果:', result.data);
}

/**
 * 測試4: 參數驗證測試
 */
async function testParameterValidation(): Promise<void> {
  const registerUser = httpsCallable(functions, 'registerUser');
  
  // 測試缺少必要參數
  const invalidData = {
    deviceID: MOCK_DEVICE_ID,
    // 缺少 nickname
    fcmToken: MOCK_FCM_TOKEN
  };
  
  try {
    await registerUser(invalidData);
    throw new Error('應該因為缺少 nickname 而失敗，但測試通過了');
  } catch (error: any) {
    if (error.message.includes('nickname')) {
      console.log('✅ 參數驗證正確 - 正確檢測到缺少 nickname');
    } else {
      throw new Error(`參數驗證錯誤類型不正確: ${error.message}`);
    }
  }
}

/**
 * 測試5: 通知創建測試
 */
async function testAlertCreation(): Promise<void> {
  const createAlert = httpsCallable(functions, 'createAlert');
  
  const result = await createAlert(MOCK_ALERT_DATA);
  
  if (!result.data || !result.data.success) {
    throw new Error(`通知創建失敗: ${result.data?.message || '未知錯誤'}`);
  }
  
  console.log('✅ 通知創建成功');
  console.log('📋 創建結果:', result.data);
}

/**
 * 主測試函數
 */
async function main(): Promise<void> {
  console.log('🚀 開始 Firebase 環境配置和 Cloud Functions 測試');
  console.log('=' .repeat(60));
  
  // 執行所有測試
  await runTest('環境隔離驗證', testEnvironmentIsolation);
  await runTest('健康檢查', testHealthCheck);
  await runTest('成功的用戶註冊測試', testSuccessfulUserRegistration);
  await runTest('參數驗證測試', testParameterValidation);
  await runTest('通知創建測試', testAlertCreation);
  
  // 輸出測試結果摘要
  console.log('\n' + '=' .repeat(60));
  console.log('📊 測試結果摘要');
  console.log('=' .repeat(60));
  
  const successCount = testResults.filter(r => r.success).length;
  const totalCount = testResults.length;
  const totalDuration = testResults.reduce((sum, r) => sum + r.duration, 0);
  
  testResults.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name} (${result.duration}ms)`);
    if (!result.success) {
      console.log(`   錯誤: ${result.message}`);
    }
  });
  
  console.log(`\n📈 總計: ${successCount}/${totalCount} 測試通過`);
  console.log(`⏱️  總耗時: ${totalDuration}ms`);
  
  // 根據測試結果設置退出代碼
  if (successCount === totalCount) {
    console.log('🎉 所有測試通過！');
    process.exit(0);
  } else {
    console.log('💥 部分測試失敗！');
    process.exit(1);
  }
}

// 執行測試
main().catch(error => {
  console.error('💥 測試執行過程中發生未捕獲的錯誤:', error);
  process.exit(1);
});
