#!/usr/bin/env node

/**
 * 測試 diagnoseEnv Cloud Function
 * 檢查環境變數是否正確讀取
 */

import { httpsCallable } from 'firebase/functions';
import { functions, getEnvironmentInfo, isDevelopment } from '../utils/firebaseEnvironment';

/**
 * 測試 diagnoseEnv 函數
 */
async function testDiagnoseEnv(): Promise<void> {
  console.log('🔍 測試 diagnoseEnv 函數...');
  
  try {
    const diagnoseEnv = httpsCallable(functions, 'diagnoseEnv');
    const result = await diagnoseEnv();
    
    console.log('✅ diagnoseEnv 函數調用成功');
    console.log('📋 環境變數診斷結果:');
    console.log(JSON.stringify(result.data, null, 2));
    
    // 檢查關鍵環境變數
    const data = result.data as any;
    
    if (data.nodeEnv) {
      console.log(`✅ NODE_ENV: ${data.nodeEnv}`);
    } else {
      console.log('⚠️  NODE_ENV 未設置');
    }
    
    if (data.gcloudProject) {
      console.log(`✅ GCLOUD_PROJECT: ${data.gcloudProject}`);
    } else {
      console.log('⚠️  GCLOUD_PROJECT 未設置');
    }
    
    if (data.firestoreEventsCollection) {
      console.log(`✅ FIRESTORE_EVENTS_COLLECTION: ${data.firestoreEventsCollection}`);
    } else {
      console.log('⚠️  FIRESTORE_EVENTS_COLLECTION 未設置');
    }
    
    console.log('\n🎉 diagnoseEnv 測試完成！');
    
  } catch (error) {
    console.error('❌ diagnoseEnv 函數調用失敗:', error);
    throw error;
  }
}

/**
 * 主函數
 */
async function main(): Promise<void> {
  console.log('🚀 開始測試 diagnoseEnv Cloud Function');
  console.log('=' .repeat(50));
  
  // 顯示本地環境信息
  const localEnvInfo = getEnvironmentInfo();
  console.log('📊 本地環境信息:');
  console.log(`   環境: ${localEnvInfo.isDevelopment ? '開發環境' : '生產環境'}`);
  console.log(`   Firebase 項目: ${localEnvInfo.firebaseProject}`);
  console.log('');
  
  try {
    await testDiagnoseEnv();
    console.log('\n🎉 所有測試通過！');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 測試失敗:', error);
    process.exit(1);
  }
}

// 執行測試
main();
