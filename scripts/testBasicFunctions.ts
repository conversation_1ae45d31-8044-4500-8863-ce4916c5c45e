#!/usr/bin/env node

/**
 * Firebase 基本功能測試腳本
 * 
 * 簡化版測試，專注於核心功能驗證
 */

import { httpsCallable } from 'firebase/functions';
import { functions, getEnvironmentInfo, isDevelopment } from '../utils/firebaseEnvironment';

/**
 * 基本環境檢查
 */
async function checkEnvironment(): Promise<void> {
  console.log('🔍 檢查環境配置...');
  
  const envInfo = getEnvironmentInfo();
  console.log('📊 當前環境:', envInfo.isDevelopment ? '開發環境' : '生產環境');
  console.log('🏗️  Firebase 項目:', envInfo.firebaseProject);
  console.log('📁 集合配置:', envInfo.collections.firestore);
  
  if (isDevelopment && envInfo.firebaseProject !== 'qmnoti-test') {
    throw new Error('開發環境配置錯誤');
  }
  
  if (!isDevelopment && envInfo.firebaseProject !== 'qmnoti') {
    throw new Error('生產環境配置錯誤');
  }
  
  console.log('✅ 環境配置正確');
}

/**
 * 測試健康檢查
 */
async function testHealthCheck(): Promise<void> {
  console.log('\n🏥 測試健康檢查...');
  
  try {
    const healthCheck = httpsCallable(functions, 'healthCheck');
    const result = await healthCheck();
    
    console.log('✅ 健康檢查成功');
    console.log('📋 結果:', result.data);
  } catch (error) {
    console.error('❌ 健康檢查失敗:', error);
    throw error;
  }
}

/**
 * 測試用戶註冊
 */
async function testUserRegistration(): Promise<void> {
  console.log('\n👤 測試用戶註冊...');
  
  const testUser = {
    deviceID: `test_device_${Date.now()}`,
    nickname: '測試用戶',
    fcmToken: `test_token_${Date.now()}`,
    name: 'Test User',
    role: 'nurse'
  };
  
  try {
    const registerUser = httpsCallable(functions, 'registerUser');
    const result = await registerUser(testUser);
    
    if (result.data?.success) {
      console.log('✅ 用戶註冊成功');
      console.log('📋 結果:', result.data);
    } else {
      throw new Error(result.data?.message || '註冊失敗');
    }
  } catch (error) {
    console.error('❌ 用戶註冊失敗:', error);
    throw error;
  }
}

/**
 * 主函數
 */
async function main(): Promise<void> {
  console.log('🚀 Firebase 基本功能測試開始');
  console.log('=' .repeat(50));
  
  try {
    await checkEnvironment();
    await testHealthCheck();
    await testUserRegistration();
    
    console.log('\n🎉 所有測試通過！');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 測試失敗:', error);
    process.exit(1);
  }
}

// 執行測試
main();
