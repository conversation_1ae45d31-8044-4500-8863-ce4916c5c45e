# Firebase 環境配置範例
# 複製此文件為 .env 並根據需要修改

# ==========================================
# 環境設置
# ==========================================

# Node.js 環境設置 (控制Firebase環境)
# 可選值:
#   - 'development' : 開發模式，使用Firebase模擬器
#   - 'production'  : 生產模式，使用真實Firebase服務
NODE_ENV=development

# ==========================================
# Firebase 模擬器配置
# ==========================================

# Firestore 模擬器
FIRESTORE_EMULATOR_HOST=localhost
FIRESTORE_EMULATOR_PORT=8083

# Realtime Database 模擬器
DATABASE_EMULATOR_HOST=localhost
DATABASE_EMULATOR_PORT=9003

# Functions 模擬器
FUNCTIONS_EMULATOR_HOST=localhost
FUNCTIONS_EMULATOR_PORT=5003

# Auth 模擬器
AUTH_EMULATOR_HOST=localhost
AUTH_EMULATOR_PORT=9099

# 模擬器 UI
EMULATOR_UI_PORT=4003

# ==========================================
# 環境切換說明
# ==========================================
#
# 使用命令行工具切換環境：
#   pnpm switch-env development  # 切換到開發環境 (模擬器)
#   pnpm switch-env production   # 切換到生產環境
#   pnpm switch-env status       # 查看當前環境
#   pnpm validate-env            # 驗證環境配置
#
# 開發環境 (NODE_ENV=development)：
#   - 使用本地 Firebase 模擬器
#   - 需要先啟動模擬器: firebase emulators:start
#   - 數據不會影響生產環境
#   - 支援離線開發
#
# 生產環境 (NODE_ENV=production)：
#   - 使用真實的 Firebase 服務
#   - 需要網路連接
#   - 影響實際用戶數據
#   - 切換前請確認操作
#
# ==========================================

# 其他配置（根據需要添加）
# EXPO_PUBLIC_API_URL=
# EXPO_PUBLIC_APP_VERSION=
