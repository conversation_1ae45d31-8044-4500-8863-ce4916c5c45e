/**
 * Firebase 環境配置工具 (Node.js 專用)
 * 用於測試腳本和 Node.js 環境中的 Firebase 配置
 */

import { initializeApp, getApps, type FirebaseApp } from 'firebase/app';
import { getAuth, type Auth } from 'firebase/auth';
import { getDatabase, type Database } from 'firebase/database';
import { getFirestore, type Firestore } from 'firebase/firestore';
import { getFunctions, type Functions } from 'firebase/functions';
import dotenv from 'dotenv';

// 載入環境變數
dotenv.config();

/**
 * Firebase 配置
 */
const productionConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

const testConfig = {
  apiKey: "AIzaSyDfTg1CV5U_bGR50SuvihQxemS_jZN-bz4",
  authDomain: "qmnoti-test.firebaseapp.com",
  projectId: "qmnoti-test",
  storageBucket: "qmnoti-test.firebasestorage.app",
  messagingSenderId: "175834354502",
  appId: "1:175834354502:web:053bd4096fb906ff4fcb47",
  measurementId: "G-N8CJ8GQWYF",
  databaseURL: "https://qmnoti-test-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

/**
 * 判斷是否為開發環境
 */
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * 根據環境選擇 Firebase 配置
 */
const firebaseConfig = isDevelopment ? testConfig : productionConfig;

/**
 * 初始化 Firebase App (Node.js 環境)
 */
let app: FirebaseApp;
const appName = isDevelopment ? 'qmnoti-test-node' : 'qmnoti-prod-node';

// 檢查是否已經初始化
const existingApp = getApps().find(app => app.name === appName);
if (existingApp) {
  app = existingApp;
} else {
  app = initializeApp(firebaseConfig, appName);
}

// 初始化 Firebase 服務
const firestoreDB: Firestore = getFirestore(app);
const realtimeDB: Database = getDatabase(app);
const auth: Auth = getAuth(app);
const functions: Functions = getFunctions(app, 'asia-east1');

// 環境信息輸出
console.log(`[Node.js Firebase] 環境: ${isDevelopment ? 'DEVELOPMENT' : 'PRODUCTION'}`);
console.log(`[Node.js Firebase] 項目 ID: ${firebaseConfig.projectId}`);
console.log(`[Node.js Firebase] Functions 區域: asia-east1`);

/**
 * 集合配置
 */
export const getCollectionNames = () => ({
  firestore: {
    users: process.env.FIRESTORE_USERS_COLLECTION || 'users',
    alertEvents: process.env.FIRESTORE_EVENTS_COLLECTION || 'alertEvents',
    groups: process.env.FIRESTORE_GROUPS_COLLECTION || 'groups',
    staff: process.env.FIRESTORE_STAFF_COLLECTION || 'staff'
  },
  realtimeDB: {
    presence: process.env.REALTIME_PRESENCE_PATH || 'presence',
    alertEvents: process.env.REALTIME_EVENTS_PATH || 'alertEvents',
    stats: process.env.REALTIME_STATS_PATH || 'stats'
  }
});

/**
 * 環境信息
 */
export const getEnvironmentInfo = () => ({
  isDevelopment,
  nodeEnv: process.env.NODE_ENV,
  firebaseProject: firebaseConfig.projectId,
  collections: getCollectionNames(),
  isolationMethod: 'separate-firebase-projects'
});

export { app, auth, firestoreDB, realtimeDB, functions, isDevelopment };
