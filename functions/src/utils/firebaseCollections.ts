/**
 * Firebase 集合名稱管理工具 (Cloud Functions)
 * 環境變數由 Firebase/Google Cloud 平台在部署時注入。
 * 環境隔離通過部署到不同的 Firebase 項目實現。
 */

// -----------------------------------------------------------------------------
// 1. 在模組加載時，計算一次配置並儲存
// -----------------------------------------------------------------------------

const isDevelopment = process.env.NODE_ENV === 'development';
console.log('🔍 NODE_ENV:', process.env);
console.log('🔍 FIRESTORE_USERS_COLLECTION:', process.env.FIRESTORE_USERS_COLLECTION);
console.log('🔍 FIRESTORE_EVENTS_COLLECTION:', process.env.FIRESTORE_EVENTS_COLLECTION);
console.log('🔍 FIRESTORE_GROUPS_COLLECTION:', process.env.FIRESTORE_GROUPS_COLLECTION);
console.log('🔍 FIRESTORE_STAFF_COLLECTION:', process.env.FIRESTORE_STAFF_COLLECTION);
console.log('🔍 REALTIME_PRESENCE_PATH:', process.env.REALTIME_PRESENCE_PATH);
console.log('🔍 REALTIME_EVENTS_PATH:', process.env.REALTIME_EVENTS_COLLECTION);
console.log('🔍 REALTIME_STATS_PATH:', process.env.REALTIME_STATS_PATH);

const collectionConfig = {
  firestore: {
    users: process.env.FIRESTORE_USERS_COLLECTION || 'users',
    alertEvents: process.env.FIRESTORE_EVENTS_COLLECTION || 'alertEvents',
    groups: process.env.FIRESTORE_GROUPS_COLLECTION || 'groups',
    staff: process.env.FIRESTORE_STAFF_COLLECTION || 'staff'
  },
  realtimeDB: {
    presence: process.env.REALTIME_PRESENCE_PATH || 'presence',
    alertEvents: process.env.REALTIME_EVENTS_PATH || 'alertEvents',
    stats: process.env.REALTIME_STATS_PATH || 'stats'
  }
};

// -----------------------------------------------------------------------------
// 2. 在初始化時，打印一次日誌（可選，但保持清晰）
// -----------------------------------------------------------------------------

if (isDevelopment) {
  console.log('🧪 Cloud Functions 開發環境：使用測試 Firebase 項目 (qmnoti-test)');
  console.log('📊 Firestore Collections:', collectionConfig.firestore);
  console.log('📈 RealtimeDB Paths:', collectionConfig.realtimeDB);
} else {
  console.log('🚀 Cloud Functions 生產環境');
}

// -----------------------------------------------------------------------------
// 3. 匯出預先計算好的配置和函式
// -----------------------------------------------------------------------------

/**
 * 獲取 Firestore 集合名稱
 */
export function getFirestoreCollections() {
  return collectionConfig.firestore;
}

/**
 * 獲取 Realtime Database 路徑
 */
export function getRealtimeDatabasePaths() {
  return collectionConfig.realtimeDB;
}

/**
 * 獲取環境信息
 */
export function getEnvironmentInfo() {
  return {
    isDevelopment,
    nodeEnv: process.env.NODE_ENV,
    collections: collectionConfig,
    firebaseProject: isDevelopment ? 'qmnoti-test' : 'qmnoti',
    isolationMethod: 'separate-firebase-projects'
  };
}


/**
 * 檢查是否為測試數據
 * 注意：在新的項目隔離模式下，測試數據存儲在獨立的 Firebase 項目中
 * 此函數主要用於向後兼容和數據清理
 */
export function isTestData(data: any): boolean {
  // 檢查常見的測試數據標識
  if (typeof data === 'string') {
    return data.includes('test') || data.includes('Test') || data.includes('測試');
  }
  
  if (typeof data === 'object' && data !== null) {
    // 檢查 deviceID 或其他標識
    const deviceID = data.deviceID || data.id;
    if (deviceID && typeof deviceID === 'string') {
      return deviceID.includes('test') || deviceID.includes('Test');
    }
    
    // 檢查 nickname 或 name
    const nickname = data.nickname || data.name;
    if (nickname && typeof nickname === 'string') {
      return nickname.includes('Test') || nickname.includes('測試') || nickname.includes('test');
    }
  }
  
  return false;
}

/**
 * 生成測試設備 ID
 */
export function generateTestDeviceID(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `test_device_${timestamp}_${random}`;
}

/**
 * 生成測試事件 ID
 */
export function generateTestEventID(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `test_event_${timestamp}_${random}`;
}
