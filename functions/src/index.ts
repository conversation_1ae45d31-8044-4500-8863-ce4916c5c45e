/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

/**
 * QMNotiAugment 母嬰轉送通知系統 Cloud Functions
 * 
 * 實現功能：
 * 1. registerUser - 用戶註冊與更新
 * 2. createAlert - 創建通知事件並發送 FCM
 * 3. acknowledgeAlert - 接收者確認通知
 * 4. cancelAlert - 發起者取消通知
 * 5. initializeStaffData - 初始化員工數據
 */

import {onCall, HttpsError} from "firebase-functions/v2/https";
import {onDocumentCreated} from "firebase-functions/v2/firestore";
import * as logger from "firebase-functions/logger";
import {setGlobalOptions} from "firebase-functions/v2";

// Firebase Admin SDK 導入
import {initializeApp} from "firebase-admin/app";
import {getFirestore, FieldValue} from "firebase-admin/firestore";
import {getDatabase} from "firebase-admin/database";
import {getMessaging} from "firebase-admin/messaging";

// 導入統一的集合管理工具
import {
  getFirestoreCollections,
  getRealtimeDatabasePaths,
  getEnvironmentInfo
} from "./utils/firebaseCollections";

// 獲取環境信息
const envInfo = getEnvironmentInfo();
const isDevelopment = envInfo.isDevelopment;

if (isDevelopment) {
  logger.info("🧪 開發環境已檢測，將使用測試集合和路徑");
} else {
  logger.info("🚀 生產環境已檢測，將使用正式集合和路徑");
}

setGlobalOptions({region: "asia-east1"});

// 初始化 Firebase Admin SDK
initializeApp();

// 獲取 Firebase 服務實例
const firestore = getFirestore();
const realtimeDb = getDatabase();
const messaging = getMessaging();

// 獲取集合和路徑配置
const firestoreCollections = getFirestoreCollections();
const realtimePaths = getRealtimeDatabasePaths();

/**
 * 類型定義 (與 types/firebase.ts 對應)
 */
interface RegisterUserRequest {
  deviceID: string;
  nickname: string;
  fcmToken: string;
  name?: string;
  role?: string;
  initials?: string;
  color?: string;
  phoneNumber?: string;
  avatar?: string;
}

interface CreateAlertRequest {
  initiatorDeviceID: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  recipientDeviceIDs: string[];
}

interface AcknowledgeAlertRequest {
  eventID: string;
  recipientDeviceID: string;
}

interface CancelAlertRequest {
  eventID: string;
  initiatorDeviceID: string;
}

/**
 * 工具函數
 */

/**
 * 生成唯一的事件 ID
 */
function generateEventID(): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `event_${timestamp}_${randomStr}`;
}

/**
 * 驗證 FCM Token 格式
 * 改進版本：檢測錯誤標識token並驗證基本格式，支援模擬器環境的特殊token
 */
function isValidFCMToken(token: string): boolean {
  try {
    // 基本存在性檢查
    if (!token || typeof token !== "string" || token.length === 0) {
      logger.warn('FCM Token為空或無效類型');
      return false;
    }

    // 檢測錯誤標識token（由客戶端在獲取失敗時生成）
    if (token.startsWith('expo_error_token_') ||
        token.startsWith('error_token_') ||
        token.includes('_error_')) {
      logger.warn(`檢測到錯誤標識FCM Token: ${token.substring(0, 20)}...`);
      return false;
    }

    // 在開發環境下，接受多種測試token格式
    if (isDevelopment) {
      if (token.startsWith('test_fcm_token_') ||
          token.startsWith('simulator_fcm_token_') ||
          token.startsWith('no_permission_token_')) {
        logger.info(`開發環境：接受測試FCM Token: ${token.substring(0, 20)}...`);
        return true;
      }

      // 在開發環境下，也接受真實的FCM Token（真實設備連接的情況）
      if (token.length > 100 && !token.includes('test_') && !token.includes('simulator_')) {
        logger.info(`開發環境：接受真實設備FCM Token: ${token.substring(0, 20)}... (長度: ${token.length})`);
        return true;
      }
    }

    // 生產環境的標準檢查
    // 基本長度檢查：FCM token通常很長
    if (token.length < 20) {
      logger.warn(`FCM Token長度過短: ${token.length}`);
      return false;
    }

    // 拒絕明顯的測試token（在生產環境）
    if (!isDevelopment && (token.startsWith('test_') || token.startsWith('simulator_') || token.startsWith('no_permission_'))) {
      logger.warn(`生產環境不接受測試FCM Token: ${token.substring(0, 20)}...`);
      return false;
    }

    // 簡化的字符檢查：避免複雜正則表達式
    const hasInvalidChars = /[<>{}[\]\\|`~!@#$%^&*()+=;'"?/\s]/.test(token);
    if (hasInvalidChars) {
      logger.warn('FCM Token包含明顯無效字符');
      return false;
    }

    logger.info(`FCM Token驗證通過: ${token.substring(0, 20)}... (長度: ${token.length})`);
    return true;
  } catch (error) {
    logger.error('FCM Token驗證過程中發生錯誤:', error);
    return false;
  }
}

/**
 * 建構 FCM 通知 Payload
 */
function buildFCMPayload(alertData: any): any {
  const caseTypeLabels: Record<string, string> = {
    "mother_baby_transfer": "母親與嬰兒轉送",
    "mother_only_transfer": "僅母親轉送",
    "baby_to_nicu": "嬰兒轉送至 NICU",
  };

  const title = "[緊急] 母嬰轉送通知";
  const caseLabel = caseTypeLabels[alertData.caseType] || alertData.caseType;
  const body = `個案: ${caseLabel}\n母親: ${alertData.motherInitial}${
    alertData.bedNumber ? `\n床號: ${alertData.bedNumber}` : ""
  }\n目標: ${alertData.designatedWard}\n請確認收到`;

  return {
    notification: {
      title,
      body,
    },
    data: {
      eventID: alertData.eventID,
      caseType: alertData.caseType,
      motherInitial: alertData.motherInitial,
      bedNumber: alertData.bedNumber || "",
      designatedWard: alertData.designatedWard,
      clinicalNotes: alertData.clinicalNotes || "",
      initiatorNickname: alertData.initiatorNickname,
    },
    android: {
      priority: "high" as const,
      notification: {
        channelId: "maternal_alerts",
        priority: "high" as const,
        sound: "default",
        vibrationPattern: [0, 250, 250, 250],
      },
    },
    apns: {
      headers: {
        "apns-priority": "10",
      },
      payload: {
        aps: {
          alert: {
            title,
            body,
          },
          sound: "default",
          badge: 1,
        },
      },
    },
  };
}

/**
 * Cloud Functions 實現
 */

/**
 * 1. 用戶註冊/更新 Cloud Function
 */
export const registerUser = onCall(async (request: any) => {
  try {
    logger.info("開始處理用戶註冊請求");
    const data = request.data as RegisterUserRequest;

    // 資料驗證
    if (!data.deviceID || !data.nickname || !data.fcmToken) {
      logger.warn("缺少必填欄位");
      throw new HttpsError(
        "invalid-argument",
        "deviceID, nickname, 和 fcmToken 為必填欄位"
      );
    }

    logger.info(`驗證FCM Token: ${data.fcmToken.substring(0, 20)}...`);
    if (!isValidFCMToken(data.fcmToken)) {
      logger.warn("FCM Token驗證失敗");
      throw new HttpsError("invalid-argument", "無效的 FCM Token");
    }

    logger.info("FCM Token驗證通過，開始構建用戶資料");
    const serverTimestamp = FieldValue.serverTimestamp();

    // 建構用戶資料
    const userData = {
      deviceID: data.deviceID,
      nickname: data.nickname,
      fcmToken: data.fcmToken,
      name: data.name || "",
      role: data.role || "",
      initials: data.initials || "",
      color: data.color || "#3B82F6",
      phoneNumber: data.phoneNumber || "",
      avatar: data.avatar || "",
      lastSeen: serverTimestamp,
      isActive: true,
      createdAt: serverTimestamp,
    };

    logger.info("開始寫入Firestore");
    // 更新或創建 Firestore 用戶記錄
    await firestore
      .collection(firestoreCollections.users)
      .doc(data.deviceID)
      .set(userData, {merge: true});

    logger.info("開始寫入Realtime Database");
    // 更新 Realtime Database 在線狀態
    await realtimeDb.ref(`${realtimePaths.presence}/${data.deviceID}`).set({
      isOnline: true,
      lastSeen: Date.now(),
      fcmTokenActive: true,
    });

    logger.info(`用戶註冊成功: ${data.deviceID} (${data.nickname})`);

    return {
      success: true,
      message: "用戶註冊成功",
      user: userData,
    };
  } catch (error) {
    logger.error("用戶註冊錯誤:", error);
    
    // 提供更詳細的錯誤信息
    if (error instanceof HttpsError) {
      throw error;
    }
    
    // 根據錯誤類型提供更具體的錯誤訊息
    let errorMessage = "用戶註冊失敗";
    if (error instanceof Error) {
      if (error.message.includes('permission')) {
        errorMessage = "權限錯誤：無法訪問Firebase服務";
      } else if (error.message.includes('network') || error.message.includes('timeout')) {
        errorMessage = "網絡錯誤：無法連接到Firebase服務";
      } else if (error.message.includes('emulator')) {
        errorMessage = "模擬器連接錯誤：請確認Firebase模擬器正在運行";
      } else {
        errorMessage = `用戶註冊失敗: ${error.message}`;
      }
    }
    
    logger.error(`詳細錯誤信息: ${errorMessage}`);
    throw new HttpsError("internal", errorMessage);
  }
});

/**
 * 2. 創建通知事件 Cloud Function
 */
export const createAlert = onCall(async (request: any) => {
  try {
    const data = request.data as CreateAlertRequest;
    
    // 資料驗證
    if (!data.initiatorDeviceID || !data.caseType || 
        !data.motherInitial || !data.designatedWard || 
        !data.recipientDeviceIDs || data.recipientDeviceIDs.length === 0) {
      throw new HttpsError(
        "invalid-argument",
        "必填欄位不完整或接收者列表為空"
      );
    }

    // 獲取發起者信息
    const initiatorDoc = await firestore
      .collection(firestoreCollections.users)
      .doc(data.initiatorDeviceID)
      .get();
    
    if (!initiatorDoc.exists) {
      throw new HttpsError("not-found", "發起者用戶不存在");
    }

    const initiator = initiatorDoc.data()!;
    const eventID = generateEventID();
    const serverTimestamp = FieldValue.serverTimestamp();
    const timestamp = Date.now();

    // 建構事件資料
    const alertEvent = {
      eventID,
      initiatorDeviceID: data.initiatorDeviceID,
      initiatorNickname: initiator.nickname,
      caseType: data.caseType,
      motherInitial: data.motherInitial,
      bedNumber: data.bedNumber || "",
      designatedWard: data.designatedWard,
      clinicalNotes: data.clinicalNotes || "",
      createdAt: serverTimestamp,
      status: "active",
      totalRecipients: data.recipientDeviceIDs.length,
      acknowledgedCount: 0,
    };

    // 在 Firestore 中創建事件記錄
    await firestore
      .collection(firestoreCollections.alertEvents)
      .doc(eventID)
      .set(alertEvent);

    // 在 Realtime Database 中創建實時狀態
    const realtimeEventData = {
      initiatorDeviceID: data.initiatorDeviceID,
      initiatorNickname: initiator.nickname,
      caseType: data.caseType,
      motherInitial: data.motherInitial,
      bedNumber: data.bedNumber || "",
      designatedWard: data.designatedWard,
      clinicalNotes: data.clinicalNotes || "",
      timestampCreated: timestamp,
      status: "active",
      recipients: {},
    };

    // 處理每個接收者
    const failedRecipients: {deviceID: string; reason: string}[] = [];
    const fcmTokens: string[] = [];
    const recipients: Record<string, any> = {};

    for (const recipientID of data.recipientDeviceIDs) {
      try {
        const recipientDoc = await firestore
          .collection(firestoreCollections.users)
          .doc(recipientID)
          .get();
        
        if (!recipientDoc.exists) {
          failedRecipients.push({
            deviceID: recipientID,
            reason: "用戶不存在",
          });
          recipients[recipientID] = {
            nickname: "未知用戶",
            status: "send_failed",
            lastUpdateTimestamp: timestamp,
            errorMessage: "用戶不存在",
          };
          continue;
        }

        const recipient = recipientDoc.data()!;
        
        if (!recipient.fcmToken || !isValidFCMToken(recipient.fcmToken)) {
          failedRecipients.push({
            deviceID: recipientID,
            reason: "無效的 FCM Token",
          });
          recipients[recipientID] = {
            nickname: recipient.nickname,
            status: "send_failed",
            lastUpdateTimestamp: timestamp,
            errorMessage: "無效的 FCM Token",
          };
          continue;
        }

        fcmTokens.push(recipient.fcmToken);
        recipients[recipientID] = {
          nickname: recipient.nickname,
          status: "fcm_sent_pending_ack",
          fcmTokenUsed: recipient.fcmToken,
          lastUpdateTimestamp: timestamp,
        };
      } catch (error) {
        logger.error(`處理接收者 ${recipientID} 時發生錯誤:`, error);
        failedRecipients.push({
          deviceID: recipientID,
          reason: "處理用戶資料時發生錯誤",
        });
        recipients[recipientID] = {
          nickname: "處理錯誤",
          status: "send_failed",
          lastUpdateTimestamp: timestamp,
          errorMessage: "處理用戶資料時發生錯誤",
        };
      }
    }

    // 更新 Realtime Database
    realtimeEventData.recipients = recipients;
    await realtimeDb.ref(`${realtimePaths.alertEvents}/${eventID}`).set(realtimeEventData);

    // 發送 FCM 推播通知
    if (fcmTokens.length > 0) {
      if (isDevelopment) {
        // 開發環境：跳過實際 FCM 發送，模擬成功
        logger.info(`開發環境：跳過 FCM 發送，模擬 ${fcmTokens.length} 個通知發送成功`);
        // 在開發環境中，所有接收者狀態保持為 "fcm_sent_pending_ack"
      } else {
        // 生產環境：實際發送 FCM
        try {
          const payload = buildFCMPayload({
            ...alertEvent,
            eventID,
          });

          const response = await messaging.sendEachForMulticast({
            tokens: fcmTokens,
            ...payload,
          });

          logger.info(`FCM 發送結果: 成功 ${response.successCount}, 失敗 ${response.failureCount}`);
          
          // 根據 FCM 發送結果更新狀態
          if (response.failureCount > 0) {
            // 處理 FCM 發送失敗的情況
            for (let i = 0; i < response.responses.length; i++) {
              if (!response.responses[i].success) {
                const failedToken = fcmTokens[i];
                // 找到對應的接收者並更新狀態
                for (const [recipientID, recipientData] of Object.entries(recipients)) {
                  if ((recipientData as any).fcmTokenUsed === failedToken) {
                    await realtimeDb
                      .ref(`${realtimePaths.alertEvents}/${eventID}/recipients/${recipientID}/status`)
                      .set("send_failed");
                    await realtimeDb
                      .ref(`${realtimePaths.alertEvents}/${eventID}/recipients/${recipientID}/errorMessage`)
                      .set(response.responses[i].error?.message || "FCM 發送失敗");
                  }
                }
              }
            }
          }
        } catch (fcmError) {
          logger.error("FCM 發送錯誤:", fcmError);
          // 將所有預期成功的接收者標記為失敗
          for (const [recipientID, recipientData] of Object.entries(recipients)) {
            if ((recipientData as any).status === "fcm_sent_pending_ack") {
              await realtimeDb
                .ref(`${realtimePaths.alertEvents}/${eventID}/recipients/${recipientID}/status`)
                .set("send_failed");
            }
          }
        }
      }
    }

    logger.info(`通知事件創建成功: ${eventID}`);

    return {
      success: true,
      message: "通知事件創建成功",
      eventID,
      failedRecipients: failedRecipients.length > 0 ? failedRecipients : undefined,
    };
  } catch (error) {
    logger.error("創建通知事件錯誤:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "創建通知事件失敗");
  }
});

/**
 * 3. 確認通知 Cloud Function
 */
export const acknowledgeAlert = onCall(async (request: any) => {
  try {
    const data = request.data as AcknowledgeAlertRequest;
    
    // 資料驗證
    if (!data.eventID || !data.recipientDeviceID) {
      throw new HttpsError(
        "invalid-argument",
        "eventID 和 recipientDeviceID 為必填欄位"
      );
    }

    const acknowledgedAt = Date.now();

    // 更新 Realtime Database 接收者狀態
    await realtimeDb
      .ref(`${realtimePaths.alertEvents}/${data.eventID}/recipients/${data.recipientDeviceID}`)
      .update({
        status: "acknowledged",
        acknowledgedTimestamp: acknowledgedAt,
        lastUpdateTimestamp: acknowledgedAt,
      });

    // 更新 Firestore 中的 acknowledgedCount
    const eventRef = firestore.collection(firestoreCollections.alertEvents).doc(data.eventID);
    await firestore.runTransaction(async (transaction: any) => {
      const eventDoc = await transaction.get(eventRef);
      if (eventDoc.exists) {
        const currentCount = eventDoc.data()!.acknowledgedCount || 0;
        transaction.update(eventRef, {
          acknowledgedCount: currentCount + 1,
        });
      }
    });

    logger.info(`通知確認成功: ${data.eventID} by ${data.recipientDeviceID}`);

    return {
      success: true,
      message: "通知確認成功",
      acknowledgedAt,
    };
  } catch (error) {
    logger.error("確認通知錯誤:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "確認通知失敗");
  }
});

/**
 * 4. 取消通知 Cloud Function
 */
export const cancelAlert = onCall(async (request: any) => {
  try {
    const data = request.data as CancelAlertRequest;
    
    // 資料驗證
    if (!data.eventID || !data.initiatorDeviceID) {
      throw new HttpsError(
        "invalid-argument",
        "eventID 和 initiatorDeviceID 為必填欄位"
      );
    }

    const cancelledAt = Date.now();

    // 驗證發起者權限
    const eventSnapshot = await realtimeDb
      .ref(`${realtimePaths.alertEvents}/${data.eventID}`)
      .once("value");

    if (!eventSnapshot.exists()) {
      throw new HttpsError("not-found", "通知事件不存在");
    }

    const eventData = eventSnapshot.val();
    if (eventData.initiatorDeviceID !== data.initiatorDeviceID) {
      throw new HttpsError("permission-denied", "只有發起者可以取消通知");
    }

    // 更新 Realtime Database 事件狀態
    await realtimeDb
      .ref(`${realtimePaths.alertEvents}/${data.eventID}/status`)
      .set("cancelled");

    // 更新 Firestore 事件狀態
    await firestore
      .collection(firestoreCollections.alertEvents)
      .doc(data.eventID)
      .update({
        status: "cancelled",
      });

    // 可選：向未確認的接收者發送取消通知
    // 這裡可以實施發送「事件已取消」的推播通知

    logger.info(`通知事件取消成功: ${data.eventID}`);

    return {
      success: true,
      message: "通知事件取消成功",
      cancelledAt,
    };
  } catch (error) {
    logger.error("取消通知事件錯誤:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError("internal", "取消通知事件失敗");
  }
});

/**
 * 5. 初始化員工數據 Cloud Function (一次性執行)
 */
export const initializeStaffData = onCall(async (request: any) => {
  try {
    // 模擬的員工資料 (對應 data/staff.ts)
    const staffData = [
      {
        id: "staff_1",
        name: "Jane Doe",
        role: "Obstetrician",
        initials: "JD",
        color: "#3B82F6",
        phoneNumber: "******-0101",
      },
      {
        id: "staff_2",
        name: "Sarah Miller",
        role: "Midwife",
        initials: "SM",
        color: "#8B5CF6",
        phoneNumber: "******-0102",
      },
      // 可以添加更多員工資料
    ];

    const batch = firestore.batch();
    const now = FieldValue.serverTimestamp();

    // 批次創建員工用戶記錄
    staffData.forEach((staff) => {
      const userRef = firestore.collection(firestoreCollections.users).doc(staff.id);
      batch.set(userRef, {
        deviceID: staff.id,
        nickname: staff.name,
        fcmToken: "", // 待用戶註冊時更新
        name: staff.name,
        role: staff.role,
        initials: staff.initials,
        color: staff.color,
        phoneNumber: staff.phoneNumber,
        avatar: "",
        createdAt: now,
        lastSeen: now,
        isActive: true,
      });
    });

    await batch.commit();

    logger.info(`員工數據初始化完成，共 ${staffData.length} 筆記錄`);

    return {
      success: true,
      message: `員工數據初始化完成，共 ${staffData.length} 筆記錄`,
      initializedCount: staffData.length,
    };
  } catch (error) {
    logger.error("初始化員工數據錯誤:", error);
    throw new HttpsError("internal", "初始化員工數據失敗");
  }
});

/**
 * Firestore 觸發器：當新建通知事件時更新統計
 */
export const updateNotificationStats = onDocumentCreated(
  `${firestoreCollections.alertEvents}/{eventID}`,
  async (event: any) => {
    try {
      const now = Date.now();
      const statsRef = realtimeDb.ref(`${realtimePaths.stats}/notifications`);

      await statsRef.transaction((currentStats: any) => {
        const stats = currentStats || {
          totalAlerts: 0,
          activeAlerts: 0,
          todayAlerts: 0,
          lastUpdated: now,
        };

        stats.totalAlerts += 1;
        stats.activeAlerts += 1;

        // 檢查是否為今日的通知
        const today = new Date().toDateString();
        const lastUpdatedDate = new Date(stats.lastUpdated).toDateString();

        if (today === lastUpdatedDate) {
          stats.todayAlerts += 1;
        } else {
          stats.todayAlerts = 1; // 重置今日計數
        }

        stats.lastUpdated = now;
        return stats;
      });

      logger.info("通知統計更新成功");
    } catch (error) {
      logger.error("更新通知統計錯誤:", error);
    }
  }
);

/**
 * 健康檢查端點
 */
/**
 * 6. 健康檢查 Cloud Function
 */
export const healthCheck = onCall(async (request: any) => {
  try {
    logger.info("健康檢查請求");

    return {
      success: true,
      status: "healthy",
      timestamp: Date.now(),
      message: "QMNotiAugment Cloud Functions 運行正常",
      environment: envInfo.nodeEnv,
      collections: envInfo.collections,
      firebaseProject: envInfo.firebaseProject
    };
  } catch (error) {
    logger.error("健康檢查錯誤:", error);
    throw new HttpsError("internal", "健康檢查失敗");
  }
});
