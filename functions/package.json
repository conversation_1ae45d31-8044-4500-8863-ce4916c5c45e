{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "pnpm run build && npx firebase emulators:start --only functions", "shell": "pnpm run build && npx firebase functions:shell", "start": "pnpm run shell", "deploy": "npx firebase deploy --only functions", "logs": "npx firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.31.0", "firebase-functions-test": "^3.4.1", "typescript": "^5.8.3"}, "private": true}