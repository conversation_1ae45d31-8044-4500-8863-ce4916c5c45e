# Firebase Cloud Functions 環境配置
# 使用不同的 Firebase 項目實現完全的數據隔離
# 最後更新: 2025/1/27

# ==========================================
# 環境設置
# ==========================================
# Node.js 環境設置 (控制 Firebase 項目選擇)
# development = 使用測試 Firebase 項目 (qmnoti-test)
# production = 使用正式 Firebase 項目 (qmnoti)
NODE_ENV=development

# ==========================================
# Firebase 集合和路徑配置
# ==========================================
# 統一的集合名稱定義，所有環境都使用這些名稱
# 環境隔離通過不同的 Firebase 項目實現

# Firestore 集合名稱
FIRESTORE_USERS_COLLECTION=users
FIRESTORE_EVENTS_COLLECTION=alertEvents
FIRESTORE_GROUPS_COLLECTION=groups
FIRESTORE_STAFF_COLLECTION=staff

# Realtime Database 路徑
REALTIME_PRESENCE_PATH=presence
REALTIME_EVENTS_PATH=alertEvents
REALTIME_STATS_PATH=stats
