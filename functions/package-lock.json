{"name": "functions", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "functions", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.31.0", "firebase-functions-test": "^3.4.1", "typescript": "^5.8.3"}, "engines": {"node": "22"}}, "../node_modules/.pnpm/@types+cors@2.8.19/node_modules/@types/cors": {"version": "2.8.19", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "../node_modules/.pnpm/@typescript-eslint+eslint-plugin@8.33.1_@typescript-eslint+parser@8.33.1_eslint@9.28.0__b4a0b80e3500f662d6218e8ead4cbe70/node_modules/@typescript-eslint/eslint-plugin": {"version": "8.33.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.33.1", "@typescript-eslint/type-utils": "8.33.1", "@typescript-eslint/utils": "8.33.1", "@typescript-eslint/visitor-keys": "8.33.1", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "devDependencies": {"@types/mdast": "^4.0.3", "@types/natural-compare": "*", "@typescript-eslint/rule-schema-to-typescript-types": "8.33.1", "@typescript-eslint/rule-tester": "8.33.1", "@vitest/coverage-v8": "^3.1.3", "ajv": "^6.12.6", "cross-fetch": "*", "eslint": "*", "json-schema": "*", "markdown-table": "^3.0.3", "marked": "^15.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-mdx": "^3.0.0", "micromark-extension-mdxjs": "^3.0.0", "prettier": "^3.5.3", "rimraf": "*", "title-case": "^4.0.0", "tsx": "*", "typescript": "*", "unist-util-visit": "^5.0.0", "vitest": "^3.1.3"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.33.1", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "../node_modules/.pnpm/@typescript-eslint+parser@8.33.1_eslint@9.28.0_typescript@5.8.3/node_modules/@typescript-eslint/parser": {"version": "8.33.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "8.33.1", "@typescript-eslint/types": "8.33.1", "@typescript-eslint/typescript-estree": "8.33.1", "@typescript-eslint/visitor-keys": "8.33.1", "debug": "^4.3.4"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "glob": "*", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "../node_modules/.pnpm/cors@2.8.5/node_modules/cors": {"version": "2.8.5", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "devDependencies": {"after": "0.8.2", "eslint": "2.13.1", "express": "4.16.3", "mocha": "5.2.0", "nyc": "13.1.0", "supertest": "3.3.0"}, "engines": {"node": ">= 0.10"}}, "../node_modules/.pnpm/eslint-config-google@0.14.0_eslint@9.28.0/node_modules/eslint-config-google": {"version": "0.14.0", "dev": true, "license": "Apache-2.0", "devDependencies": {"eslint": "^5.16.0"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"eslint": ">=5.16.0"}}, "../node_modules/.pnpm/eslint-plugin-import@2.31.0_@typescript-eslint+parser@8.33.1_eslint@9.28.0_typescript@5_22561e667bc19d62f01363df6e0e3d35/node_modules/eslint-plugin-import": {"version": "2.31.0", "dev": true, "license": "MIT", "dependencies": {"@rtsao/scc": "^1.1.0", "array-includes": "^3.1.8", "array.prototype.findlastindex": "^1.2.5", "array.prototype.flat": "^1.3.2", "array.prototype.flatmap": "^1.3.2", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.0", "hasown": "^2.0.2", "is-core-module": "^2.15.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "object.groupby": "^1.0.3", "object.values": "^1.2.0", "semver": "^6.3.1", "string.prototype.trimend": "^1.0.8", "tsconfig-paths": "^3.15.0"}, "devDependencies": {"@angular-eslint/template-parser": "^13.5.0", "@eslint/import-test-order-redirect-scoped": "file:./tests/files/order-redirect-scoped", "@test-scope/some-module": "file:./tests/files/symlinked-module", "@typescript-eslint/parser": "^2.23.0 || ^3.3.0 || ^4.29.3 || ^5.10.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-eslint": "=8.0.3 || ^8.2.6", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-module-resolver": "^2.7.1", "babel-preset-airbnb": "^2.6.0", "babel-preset-flow": "^6.23.0", "babel-register": "^6.26.0", "babylon": "^6.18.0", "chai": "^4.3.10", "cross-env": "^4.0.0", "escope": "^3.6.0", "eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9", "eslint-doc-generator": "^1.6.1", "eslint-import-resolver-node": "file:./resolvers/node", "eslint-import-resolver-typescript": "^1.0.2 || ^1.1.1", "eslint-import-resolver-webpack": "file:./resolvers/webpack", "eslint-import-test-order-redirect": "file:./tests/files/order-redirect", "eslint-module-utils": "file:./utils", "eslint-plugin-eslint-plugin": "^2.3.0", "eslint-plugin-import": "2.x", "eslint-plugin-json": "^2.1.2", "find-babel-config": "=1.2.0", "fs-copy-file-sync": "^1.1.1", "glob": "^7.2.3", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "jsonc-parser": "=3.2.0", "linklocal": "^2.8.2", "lodash.isarray": "^4.0.0", "markdownlint-cli": "~0.35", "mocha": "^3.5.3", "npm-which": "^3.0.1", "nyc": "^11.9.0", "redux": "^3.7.2", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "sinon": "^2.4.1", "typescript": "^2.8.1 || ~3.9.5 || ~4.5.2", "typescript-eslint-parser": "^15 || ^20 || ^22"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9"}}, "../node_modules/.pnpm/eslint@9.28.0/node_modules/eslint": {"version": "9.28.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.14.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.28.0", "@eslint/plugin-kit": "^0.3.1", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.0", "@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "@cypress/webpack-preprocessor": "^6.0.2", "@eslint/json": "^0.12.0", "@trunkio/launcher": "^1.3.4", "@types/esquery": "^1.5.4", "@types/node": "^22.13.14", "@typescript-eslint/parser": "^8.4.0", "babel-loader": "^8.0.5", "c8": "^7.12.0", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "cypress": "^14.1.0", "ejs": "^3.0.2", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-plugin": "^6.0.0", "eslint-plugin-expect-type": "^0.6.0", "eslint-plugin-yml": "^1.14.0", "eslint-release": "^3.3.0", "eslint-rule-composer": "^0.3.0", "eslump": "^3.0.0", "esprima": "^4.0.1", "fast-glob": "^3.2.11", "fs-teardown": "^0.1.3", "glob": "^10.0.0", "globals": "^16.2.0", "got": "^11.8.3", "gray-matter": "^4.0.3", "jiti": "^2.2.0", "jiti-v2.0": "npm:jiti@2.0.x", "jiti-v2.1": "npm:jiti@2.1.x", "knip": "^5.32.0", "lint-staged": "^11.0.0", "load-perf": "^0.2.0", "markdown-it": "^12.2.0", "markdown-it-container": "^3.0.0", "marked": "^4.0.8", "metascraper": "^5.25.7", "metascraper-description": "^5.25.7", "metascraper-image": "^5.29.3", "metascraper-logo": "^5.25.7", "metascraper-logo-favicon": "^5.25.7", "metascraper-title": "^5.25.7", "mocha": "^10.7.3", "node-polyfill-webpack-plugin": "^1.0.3", "npm-license": "^0.3.3", "pirates": "^4.0.5", "progress": "^2.0.3", "proxyquire": "^2.0.1", "recast": "^0.23.0", "regenerator-runtime": "^0.14.0", "semver": "^7.5.3", "shelljs": "^0.10.0", "sinon": "^11.0.0", "typescript": "^5.3.3", "webpack": "^5.23.0", "webpack-cli": "^4.5.0", "yorkie": "^2.0.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "../node_modules/.pnpm/express@5.1.0/node_modules/express": {"version": "5.1.0", "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "devDependencies": {"after": "0.8.2", "connect-redis": "^8.0.1", "cookie-parser": "1.4.7", "cookie-session": "2.1.0", "ejs": "^3.1.10", "eslint": "8.47.0", "express-session": "^1.18.1", "hbs": "4.2.0", "marked": "^15.0.3", "method-override": "3.0.0", "mocha": "^10.7.3", "morgan": "1.10.0", "nyc": "^17.1.0", "pbkdf2-password": "1.2.1", "supertest": "^6.3.0", "vhost": "~3.0.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "../node_modules/.pnpm/firebase-admin@13.4.0_encoding@0.1.13/node_modules/firebase-admin": {"version": "13.4.0", "license": "Apache-2.0", "dependencies": {"@fastify/busboy": "^3.0.0", "@firebase/database-compat": "^2.0.0", "@firebase/database-types": "^1.0.6", "@types/node": "^22.8.7", "farmhash-modern": "^1.1.0", "google-auth-library": "^9.14.2", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.1.0", "node-forge": "^1.3.1", "uuid": "^11.0.2"}, "devDependencies": {"@firebase/api-documenter": "^0.4.0", "@firebase/app-compat": "^0.2.1", "@firebase/auth-compat": "^0.5.13", "@firebase/auth-types": "^0.12.0", "@microsoft/api-extractor": "^7.11.2", "@types/bcrypt": "^5.0.0", "@types/chai": "^4.0.0", "@types/chai-as-promised": "^7.1.0", "@types/firebase-token-generator": "^2.0.28", "@types/jsonwebtoken": "8.5.1", "@types/lodash": "^4.14.104", "@types/minimist": "^1.2.2", "@types/mocha": "^10.0.0", "@types/nock": "^11.1.0", "@types/request": "^2.47.0", "@types/request-promise": "^4.1.41", "@types/sinon": "^17.0.2", "@types/sinon-chai": "^3.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "bcrypt": "^5.0.0", "chai": "^4.2.0", "chai-as-promised": "^7.0.0", "chai-exclude": "^2.1.0", "chalk": "^4.1.1", "child-process-promise": "^2.2.1", "del": "^6.0.0", "eslint": "^8.56.0", "firebase-token-generator": "^2.0.0", "gulp": "^5.0.0", "gulp-filter": "^7.0.0", "gulp-header": "^2.0.9", "gulp-typescript": "^5.0.1", "http-message-parser": "^0.0.34", "lodash": "^4.17.15", "minimist": "^1.2.6", "mocha": "^10.0.0", "mz": "^2.7.0", "nock": "^13.0.0", "npm-run-all": "^4.1.5", "nyc": "^17.0.0", "request": "^2.75.0", "request-promise": "^4.1.1", "run-sequence": "^2.2.1", "sinon": "^18.0.0", "sinon-chai": "^3.0.0", "ts-node": "^10.2.0", "typescript": "5.5.4", "yargs": "^17.0.1"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@google-cloud/firestore": "^7.11.0", "@google-cloud/storage": "^7.14.0"}}, "../node_modules/.pnpm/firebase-functions-test@3.4.1_firebase-admin@13.4.0_encoding@0.1.13__firebase-functions_3ddb844f6582cfb07e6f91baa17ec27b/node_modules/firebase-functions-test": {"version": "3.4.1", "dev": true, "license": "MIT", "dependencies": {"@types/lodash": "^4.14.104", "lodash": "^4.17.5", "ts-deepmerge": "^2.0.1"}, "devDependencies": {"@types/chai": "~4.2.4", "@types/express": "4.17.8", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "firebase-admin": "^12.0.0", "firebase-functions": "^4.9.0", "firebase-tools": "^8.9.2", "mocha": "^6.2.2", "prettier": "^1.19.1", "sinon": "^7.5.0", "tslint": "^5.20.0", "typescript": "^4.2.5"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"firebase-admin": "^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0 || ^12.0.0 || ^13.0.0", "firebase-functions": ">=4.9.0", "jest": ">=28.0.0"}}, "../node_modules/.pnpm/firebase-functions@6.3.2_firebase-admin@13.4.0_encoding@0.1.13_/node_modules/firebase-functions": {"version": "6.3.2", "license": "MIT", "dependencies": {"@types/cors": "^2.8.5", "@types/express": "^4.17.21", "cors": "^2.8.5", "express": "^4.21.0", "protobufjs": "^7.2.2"}, "bin": {"firebase-functions": "lib/bin/firebase-functions.js"}, "devDependencies": {"@firebase/api-documenter": "^0.2.0", "@microsoft/api-documenter": "^7.13.45", "@microsoft/api-extractor": "^7.18.7", "@types/chai": "^4.1.7", "@types/chai-as-promised": "^7.1.0", "@types/jsonwebtoken": "^9.0.0", "@types/mocha": "^5.2.7", "@types/mock-require": "^2.0.0", "@types/nock": "^10.0.3", "@types/node": "^14.18.24", "@types/node-fetch": "^3.0.3", "@types/sinon": "^9.0.11", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "api-extractor-model-me": "^0.1.1", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "child-process-promise": "^2.2.1", "eslint": "^8.6.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jsdoc": "^39.2.9", "eslint-plugin-prettier": "^4.0.0", "firebase-admin": "^13.0.0", "genkit": "^1.0.0-rc.4", "js-yaml": "^3.13.1", "jsdom": "^16.2.1", "jsonwebtoken": "^9.0.0", "jwk-to-pem": "^2.0.5", "mocha": "^10.2.0", "mock-require": "^3.0.3", "mz": "^2.7.0", "nock": "^13.2.9", "node-fetch": "^2.6.7", "portfinder": "^1.0.28", "prettier": "^2.7.1", "protobufjs-cli": "^1.1.1", "semver": "^7.3.5", "sinon": "^9.2.4", "ts-node": "^10.4.0", "typescript": "^4.3.5", "yargs": "^15.3.1"}, "engines": {"node": ">=14.10.0"}, "peerDependencies": {"firebase-admin": "^11.10.0 || ^12.0.0 || ^13.0.0"}}, "../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript": {"version": "5.8.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "node_modules/@types/body-parser": {"version": "1.19.5", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/cors": {"resolved": "../node_modules/.pnpm/@types+cors@2.8.19/node_modules/@types/cors", "link": true}, "node_modules/@types/express": {"version": "5.0.3", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.6", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/http-errors": {"version": "2.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "22.15.30", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/qs": {"version": "6.14.0", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "dev": true, "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.4", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "dev": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@typescript-eslint/eslint-plugin": {"resolved": "../node_modules/.pnpm/@typescript-eslint+eslint-plugin@8.33.1_@typescript-eslint+parser@8.33.1_eslint@9.28.0__b4a0b80e3500f662d6218e8ead4cbe70/node_modules/@typescript-eslint/eslint-plugin", "link": true}, "node_modules/@typescript-eslint/parser": {"resolved": "../node_modules/.pnpm/@typescript-eslint+parser@8.33.1_eslint@9.28.0_typescript@5.8.3/node_modules/@typescript-eslint/parser", "link": true}, "node_modules/cors": {"resolved": "../node_modules/.pnpm/cors@2.8.5/node_modules/cors", "link": true}, "node_modules/dotenv": {"version": "16.5.0", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-16.5.0.tgz", "integrity": "sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/eslint": {"resolved": "../node_modules/.pnpm/eslint@9.28.0/node_modules/eslint", "link": true}, "node_modules/eslint-config-google": {"resolved": "../node_modules/.pnpm/eslint-config-google@0.14.0_eslint@9.28.0/node_modules/eslint-config-google", "link": true}, "node_modules/eslint-plugin-import": {"resolved": "../node_modules/.pnpm/eslint-plugin-import@2.31.0_@typescript-eslint+parser@8.33.1_eslint@9.28.0_typescript@5_22561e667bc19d62f01363df6e0e3d35/node_modules/eslint-plugin-import", "link": true}, "node_modules/express": {"resolved": "../node_modules/.pnpm/express@5.1.0/node_modules/express", "link": true}, "node_modules/firebase-admin": {"resolved": "../node_modules/.pnpm/firebase-admin@13.4.0_encoding@0.1.13/node_modules/firebase-admin", "link": true}, "node_modules/firebase-functions": {"resolved": "../node_modules/.pnpm/firebase-functions@6.3.2_firebase-admin@13.4.0_encoding@0.1.13_/node_modules/firebase-functions", "link": true}, "node_modules/firebase-functions-test": {"resolved": "../node_modules/.pnpm/firebase-functions-test@3.4.1_firebase-admin@13.4.0_encoding@0.1.13__firebase-functions_3ddb844f6582cfb07e6f91baa17ec27b/node_modules/firebase-functions-test", "link": true}, "node_modules/typescript": {"resolved": "../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript", "link": true}, "node_modules/undici-types": {"version": "6.21.0", "dev": true, "license": "MIT"}}}