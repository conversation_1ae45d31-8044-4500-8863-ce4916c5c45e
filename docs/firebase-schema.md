# Firebase Database Schema 設計

## 概述

QMNotiAugment 使用雙資料庫策略：
- **Firestore**: 儲存靜態/半靜態數據（用戶資料、群組信息）
- **Realtime Database**: 儲存高頻變化的實時狀態數據（通知狀態、接收確認）

## Firestore Collections

### 1. users Collection
```typescript
/users/{deviceID}
{
  deviceID: string;           // 設備唯一標識符 (document ID)
  nickname: string;           // 用戶設定的暱稱
  fcmToken: string;          // Firebase Cloud Messaging Token
  name?: string;             // 真實姓名 (可選，來自 staff.ts)
  role?: string;             // 職位角色 (可選，來自 staff.ts)
  initials?: string;         // 姓名縮寫 (可選)
  color?: string;            // 顯示顏色 (可選)
  phoneNumber?: string;      // 電話號碼 (可選)
  avatar?: string;           // 頭像 URL (可選)
  createdAt: Timestamp;      // 創建時間
  lastSeen: Timestamp;       // 最後活動時間
  isActive: boolean;         // 是否啟用
}
```

### 2. staffGroups Collection
```typescript
/staffGroups/{groupID}
{
  id: string;                // 群組 ID (document ID)
  name: string;              // 群組名稱
  icon: string;              // Material Icons 名稱
  color: string;             // 顯示顏色
  initials: string;          // 群組縮寫
  memberIds: string[];       // 成員 deviceID 列表
  createdBy: string;         // 創建者 deviceID
  createdAt: Timestamp;      // 創建時間
  updatedAt: Timestamp;      // 最後更新時間
  isActive: boolean;         // 是否啟用
}
```

### 3. alertEvents Collection (基本信息)
```typescript
/alertEvents/{eventID}
{
  eventID: string;           // 事件 ID (document ID)
  initiatorDeviceID: string; // 發起者設備 ID
  initiatorNickname: string; // 發起者暱稱
  caseType: 'mother_baby_transfer' | 'mother_only_transfer' | 'baby_to_nicu';
  motherInitial: string;     // 母親姓名縮寫
  bedNumber?: string;        // 床號 (可選)
  designatedWard: string;    // 目標病房
  clinicalNotes?: string;    // 臨床記錄 (可選)
  createdAt: Timestamp;      // 創建時間
  status: 'active' | 'resolved' | 'cancelled';
  totalRecipients: number;   // 總接收人數
  acknowledgedCount: number; // 已確認人數
}
```

## Realtime Database Structure

### 1. 通知事件實時狀態
```typescript
/alertEvents/{eventID}
{
  // 基本事件信息 (映射自 Firestore)
  initiatorDeviceID: string;
  initiatorNickname: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  timestampCreated: number;  // Unix timestamp
  status: 'active' | 'resolved' | 'cancelled';
  
  // 實時接收者狀態
  recipients: {
    [deviceID: string]: {
      nickname: string;
      status: 'send_failed' | 'fcm_sent_pending_ack' | 'acknowledged';
      fcmTokenUsed?: string;         // 用於調試
      lastUpdateTimestamp: number;   // 狀態更新時間
      acknowledgedTimestamp?: number; // 確認時間 (如果已確認)
      errorMessage?: string;         // 錯誤信息 (如果發送失敗)
    }
  };
}
```

### 2. 用戶在線狀態 (可選功能)
```typescript
/presence/{deviceID}
{
  isOnline: boolean;
  lastSeen: number;          // Unix timestamp
  fcmTokenActive: boolean;   // FCM Token 是否有效
}
```

### 3. 實時統計
```typescript
/stats/notifications
{
  totalAlerts: number;
  activeAlerts: number;
  todayAlerts: number;
  lastUpdated: number;
}
```

## 數據流設計

### 1. 用戶註冊流程
1. 客戶端獲取 deviceID 和 FCM token
2. 調用 `registerUser` Cloud Function
3. 更新/創建 Firestore `/users/{deviceID}` 文檔
4. 更新 Realtime DB `/presence/{deviceID}` 狀態

### 2. 通知創建流程
1. 發起者調用 `createAlert` Cloud Function
2. 在 Firestore `/alertEvents/{eventID}` 創建事件記錄
3. 在 Realtime DB `/alertEvents/{eventID}` 創建實時狀態
4. 向每個接收者發送 FCM 推播
5. 根據 FCM 發送結果更新接收者狀態

### 3. 通知確認流程
1. 接收者調用 `acknowledgeAlert` Cloud Function
2. 更新 Realtime DB 中接收者狀態為 'acknowledged'
3. 更新 Firestore 中的 acknowledgedCount
4. 發起者實時監聽狀態變化

### 4. 狀態監控
- 發起者監聽: `/alertEvents/{eventID}/recipients`
- 接收者監聽: `/alertEvents/{eventID}/status` (檢查是否被取消)

## Security Rules

### Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用戶只能讀寫自己的文檔
    match /users/{deviceID} {
      allow read, write: if request.auth != null && request.auth.uid == deviceID;
    }
    
    // 所有用戶可讀群組信息，只有創建者可修改
    match /staffGroups/{groupID} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.uid == resource.data.createdBy || !exists(/databases/$(database)/documents/staffGroups/$(groupID)));
    }
    
    // 通知事件：創建者和接收者可讀，只有創建者可修改
    match /alertEvents/{eventID} {
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.initiatorDeviceID || 
         request.auth.uid in resource.data.recipientDeviceIDs);
      allow create: if request.auth != null && 
        request.auth.uid == resource.data.initiatorDeviceID;
      allow update: if request.auth != null && 
        request.auth.uid == resource.data.initiatorDeviceID;
    }
  }
}
```

### Realtime Database Rules
```json
{
  "rules": {
    "alertEvents": {
      "$eventID": {
        ".read": "auth != null",
        ".write": "auth != null",
        "recipients": {
          "$deviceID": {
            ".write": "auth != null && ($deviceID == auth.uid || root.child('alertEvents').child($eventID).child('initiatorDeviceID').val() == auth.uid)"
          }
        }
      }
    },
    "presence": {
      "$deviceID": {
        ".read": "auth != null",
        ".write": "auth != null && $deviceID == auth.uid"
      }
    }
  }
}
```

## 索引配置

### Firestore Indexes
```javascript
// 複合索引
collection: "alertEvents"
fields: [
  { field: "initiatorDeviceID", order: "ASCENDING" },
  { field: "createdAt", order: "DESCENDING" }
]

collection: "alertEvents"  
fields: [
  { field: "status", order: "ASCENDING" },
  { field: "createdAt", order: "DESCENDING" }
]
```

## 數據遷移策略

### 從 staff.ts 到 Firestore
1. 創建初始化 Cloud Function `initializeStaffData`
2. 將 `STAFF_DATA` 轉換為 Firestore users documents
3. 將 `STAFF_GROUPS` 轉換為 Firestore staffGroups documents
4. 設置適當的預設值 (fcmToken 為空，待用戶註冊時更新)

### 資料一致性
- 使用 Firestore transactions 確保數據一致性
- Realtime DB 作為 Firestore 的快取層，定期同步
- 實施資料驗證和清理機制 