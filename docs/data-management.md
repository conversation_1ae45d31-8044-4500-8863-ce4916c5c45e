# 統一數據管理方案

## 概述

本文檔記錄了QMNotiAugment項目中員工和通知數據的統一管理方案，解決了之前在不同文件中存在重複和不一致數據的問題。

## 核心設計

### 1. 類型定義 (`types/staff.ts`)

#### 基礎員工信息接口 (`StaffInfo`)
```typescript
interface StaffInfo {
  id: string;           // 員工唯一標識符
  name: string;         // 員工姓名
  role: string;         // 員工職位
  initials: string;     // 姓名首字母縮寫
  color: string;        // 顯示顏色
  avatar?: string;      // 員工頭像URL（可選）
  phoneNumber: string;  // 電話號碼
}
```

#### 通知接收者接口 (`NotificationRecipient`)
擴展自基礎員工信息，添加通知相關的狀態：
```typescript
interface NotificationRecipient extends StaffInfo {
  status: RecipientStatus;     // 接收狀態
  isInitiator?: boolean;       // 是否為通知發起人
  isCurrentUser?: boolean;     // 是否為當前用戶
  lastUpdated?: Date;          // 最後更新時間
}
```

#### 員工分組接口 (`StaffGroup`)
```typescript
interface StaffGroup {
  id: string;           // 分組唯一標識符
  name: string;         // 分組名稱
  icon: any;           // 分組圖標 (Material Icons)
  color: string;        // 分組顏色
  initials: string;     // 分組首字母縮寫
  memberIds?: string[]; // 分組成員ID列表
}
```

### 2. 統一數據源 (`data/staff.ts`)

#### 員工數據 (`STAFF_DATA`)
包含所有員工的完整信息，包括：
- 10名員工的詳細信息
- 統一的電話號碼格式 (`******-xxxx`)
- 一致的顏色方案
- 部分員工包含頭像信息

#### 分組數據 (`STAFF_GROUPS`)
定義了4個預設分組：
- 管理團隊 (Management Team)
- 全體員工 (All Employees)
- Alpha項目團隊 (Project Alpha Team)
- 護理人員 (Nursing Staff)

#### 輔助函數
```typescript
getStaffById(staffId: string): StaffInfo | undefined
getGroupById(groupId: string): StaffGroup | undefined
getGroupMembers(groupId: string): StaffInfo[]
getStaffByRole(role: string): StaffInfo[]
searchStaff(query: string): StaffInfo[]
searchGroups(query: string): StaffGroup[]
```

### 3. 通知數據整合 (`types/notification.ts`)

更新了通知接口以使用統一的員工數據：
```typescript
// 重新導出統一的接收者類型
export type RecipientInfo = NotificationRecipient;
```

### 4. 統一的數據轉換

#### NotificationContext.tsx
創建了輔助函數用於將員工數據轉換為通知接收者：
```typescript
function createNotificationRecipient(
  staffId: string,
  status: RecipientStatus,
  isCurrentUser: boolean = false,
  isInitiator: boolean = false,
  lastUpdated?: Date
): NotificationRecipient | null
```

## 影響的文件

### 更新的文件
1. **types/staff.ts** - 新增：員工和分組類型定義
2. **data/staff.ts** - 新增：統一的員工和分組數據
3. **types/notification.ts** - 更新：使用統一的接收者類型
4. **contexts/NotificationContext.tsx** - 更新：使用統一數據創建mock通知
5. **app/(tabs)/add.tsx** - 更新：移除重複數據，使用統一數據源
6. **components/RecipientsModal.tsx** - 更新：使用統一數據和搜索函數
7. **components/EditGroupModal.tsx** - 更新：使用統一員工數據
8. **components/notifications/NotificationDetails.tsx** - 更新：兼容統一數據格式

### 移除的重複數據
- `add.tsx` 中的 `ALL_STAFF_GROUPS` 和 `ALL_INDIVIDUAL_STAFF`
- `RecipientsModal.tsx` 中的 `STAFF_GROUPS` 和 `INDIVIDUAL_STAFF`
- `EditGroupModal.tsx` 中的 `AVAILABLE_STAFF`
- `NotificationContext.tsx` 中的內聯員工數據

## 優勢

### 1. 數據一致性
- 所有員工信息在單一數據源中維護
- 避免了不同文件間的數據不一致問題
- 統一的數據格式和命名規範

### 2. 可維護性
- 新增或修改員工信息只需在一個地方進行
- 清晰的類型定義確保數據完整性
- 集中的輔助函數簡化數據操作

### 3. 擴展性
- 易於添加新的員工屬性
- 支持動態分組管理
- 靈活的搜索和篩選功能

### 4. 類型安全
- 完整的TypeScript類型支持
- 編譯時錯誤檢查
- 更好的IDE支持和自動完成

## 使用示例

### 獲取員工信息
```typescript
import { getStaffById, STAFF_DATA } from '@/data/staff';

const staff = getStaffById('1'); // 獲取Jane Doe的信息
const allStaff = STAFF_DATA; // 獲取所有員工
```

### 搜索功能
```typescript
import { searchStaff, searchGroups } from '@/data/staff';

const nurses = searchStaff('nurse'); // 搜索護士
const managementGroups = searchGroups('management'); // 搜索管理相關分組
```

### 創建通知接收者
```typescript
import { createNotificationRecipient } from '@/contexts/NotificationContext';

const recipient = createNotificationRecipient('1', 'pending', false, false);
```

## 未來改進

1. **動態數據載入**：從API或數據庫載入員工數據
2. **分組管理**：實現動態分組創建和編輯
3. **權限管理**：基於角色的數據訪問控制
4. **數據緩存**：優化數據載入性能
5. **離線支持**：本地數據同步機制

## 版本歷史

- **v1.0** (2024-01): 初始統一數據管理實現
  - 創建統一的員工和分組數據結構
  - 更新所有相關組件使用統一數據源
  - 修復TypeScript類型錯誤
  - 建立完整的文檔 