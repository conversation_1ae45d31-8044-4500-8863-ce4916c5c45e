# Firebase 項目隔離策略

## 📋 概述

QMNotiAugment 項目採用**獨立 Firebase 項目**的方式實現測試和生產環境的完全數據隔離，取代了之前使用集合前綴 (test_*) 的方法。

## 🎯 新策略核心

### 基本原理

1. **項目隔離**：使用不同的 Firebase 項目實現環境隔離
2. **標準集合**：所有環境都使用相同的集合名稱
3. **環境切換**：通過 `NODE_ENV` 環境變數控制 Firebase 項目選擇
4. **完全隔離**：測試和生產數據完全分離，無交叉污染風險

### 環境配置

#### 開發環境 (NODE_ENV=development)
- **Firebase 項目**: `qmnoti-test`
- **集合名稱**: 標準名稱 (users, alertEvents, groups, staff)
- **數據隔離**: 完全獨立的 Firebase 項目
- **用途**: 開發、測試、調試

#### 生產環境 (NODE_ENV=production)
- **Firebase 項目**: `qmnoti`
- **集合名稱**: 標準名稱 (users, alertEvents, groups, staff)
- **數據隔離**: 完全獨立的 Firebase 項目
- **用途**: 正式運營

## 🔧 技術實現

### Firebase 配置切換

```typescript
// firebaseConfig.ts
const isDevelopment = process.env.NODE_ENV === 'development';
const firebaseConfig = isDevelopment ? testConfig : productionConfig;

// 開發環境使用 qmnoti-test 項目
// 生產環境使用 qmnoti 項目
```

### 集合管理

```typescript
// 所有環境都使用標準集合名稱
const standardCollections = {
  firestore: {
    users: 'users',
    alertEvents: 'alertEvents',
    groups: 'groups',
    staff: 'staff'
  },
  realtimeDB: {
    presence: 'presence',
    alertEvents: 'alertEvents',
    stats: 'stats'
  }
};
```

### Cloud Functions 部署

- **開發環境**: 部署到 `qmnoti-test` 項目
- **生產環境**: 部署到 `qmnoti` 項目
- **自動連接**: Cloud Functions 自動連接到部署它們的項目

## 📁 文件結構

### 主要配置文件

```
├── firebaseConfig.ts              # 主 Firebase 配置
├── utils/firebaseCollections.ts   # 集合管理工具
├── functions/src/utils/
│   ├── firebaseCollections.ts     # Cloud Functions 集合管理
│   └── firebaseEnvironment.ts     # React Native 環境工具
├── .env                           # 環境變數配置
└── docs/
    └── firebase-project-isolation.md  # 本文檔
```

### Google Services 文件

```
├── google-services-test.json      # 測試項目配置
└── google-service-prod.json       # 生產項目配置
```

## 🚀 使用方法

### 開發環境設置

1. **設置環境變數**
   ```bash
   # .env 文件
   NODE_ENV=development
   ```

2. **運行應用**
   ```bash
   # React Native 應用自動連接到 qmnoti-test 項目
   pnpm start
   ```

3. **部署 Cloud Functions**
   ```bash
   # 部署到測試項目
   firebase use qmnoti-test
   firebase deploy --only functions
   ```

### 生產環境設置

1. **設置環境變數**
   ```bash
   # .env 文件
   NODE_ENV=production
   ```

2. **部署 Cloud Functions**
   ```bash
   # 部署到生產項目
   firebase use qmnoti
   firebase deploy --only functions
   ```

## ✅ 優勢

### 相比集合前綴方法的優勢

1. **完全隔離**: 測試和生產數據在不同的 Firebase 項目中
2. **簡化配置**: 不需要管理複雜的集合前綴
3. **更真實的測試**: 測試環境更接近生產環境
4. **更好的安全性**: 測試代碼無法意外訪問生產數據
5. **清晰的權限管理**: 可以為不同項目設置不同的訪問權限

### 技術優勢

1. **標準化**: 所有環境使用相同的集合名稱
2. **可維護性**: 減少環境相關的配置複雜性
3. **可擴展性**: 容易添加新的環境（如 staging）
4. **調試友好**: 環境問題更容易識別和解決

## 🔄 遷移指南

### 從集合前綴遷移

1. **數據遷移**: 將 test_* 集合的數據遷移到測試項目的標準集合
2. **代碼更新**: 移除所有集合前綴相關的邏輯
3. **配置更新**: 更新環境變數和配置文件
4. **測試驗證**: 確保新配置正常工作

### 遷移檢查清單

- [ ] 更新 firebaseConfig.ts
- [ ] 更新集合管理工具
- [ ] 更新 Cloud Functions
- [ ] 更新環境變數
- [ ] 遷移測試數據
- [ ] 更新文檔
- [ ] 驗證功能正常

## 🛠️ 維護和監控

### 日常維護

1. **定期清理**: 定期清理測試項目中的過期數據
2. **監控使用**: 監控兩個項目的資源使用情況
3. **備份策略**: 為兩個項目制定獨立的備份策略

### 故障排除

1. **環境檢查**: 確認 NODE_ENV 設置正確
2. **項目連接**: 驗證應用連接到正確的 Firebase 項目
3. **權限檢查**: 確認對相應項目有正確的訪問權限

## 📚 相關文檔

- [Firebase 配置文檔](./firebase-configuration.md)
- [測試數據管理](./test-data-management.md)
- [Cloud Functions 部署](./cloud-functions-deployment.md)
- [環境變數配置](./environment-configuration.md)

---

**最後更新**: 2025/1/27  
**版本**: 2.0 (項目隔離模式)
