{"name": "qmnotiaugment", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "firebase-test": "node ./scripts/firebaseTest.js", "test-firebase-strategy": "npx tsx ./scripts/testNewFirebaseStrategy.ts", "production-test": "npx tsx ./scripts/testProductionEnvironment.ts", "production-functions-test": "npx tsx ./scripts/testProductionFunctions.ts", "cleanup-test-data": "npx tsx ./scripts/cleanupTestData.ts", "cleanup-test-collections": "npx tsx ./scripts/cleanupTestCollections.ts", "cleanup-test-collections-execute": "npx tsx ./scripts/cleanupTestCollections.ts --execute", "verify-data-isolation": "npx tsx ./scripts/verifyDataIsolation.ts", "validate-environment": "npx tsx ./scripts/validateEnvironment.ts", "test-environment-functions": "npx tsx ./scripts/testEnvironmentAndFunctions.ts", "deploy-test-functions": "npx tsx ./scripts/deployAndTestFunctions.ts", "type-check": "tsc --noEmit", "build:android": "expo run:android", "test-data": "npx tsx ./scripts/setupTestData.ts", "test-data-setup": "npx tsx ./scripts/setupTestData.ts setup", "test-data-cleanup": "npx tsx ./scripts/setupTestData.ts cleanup", "test-firebase-environment": "npx tsx ./scripts/testFirebaseEnvironment.ts", "test-basic-functions": "npx tsx ./scripts/testBasicFunctions.ts"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-vector-icons/material-design-icons": "^12.0.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.4.3", "@react-navigation/native": "^7.1.6", "@types/uuid": "^10.0.0", "expo": "~53.0.10", "expo-application": "^6.1.4", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.0", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.2.1", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-notifications": "~0.31.3", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "firebase": "^11.9.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@types/dotenv": "^8.2.3", "@types/react": "~19.0.14", "@types/react-native-vector-icons": "^6.4.18", "dotenv": "^16.5.0", "eslint": "^9.28.0", "eslint-config-expo": "~9.2.0", "firebase-tools": "^14.6.0", "tsx": "^4.19.4", "typescript": "~5.8.3"}, "private": true}