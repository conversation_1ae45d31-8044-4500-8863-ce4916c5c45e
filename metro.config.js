const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// 解決 Expo SDK 53 中 Node.js 模組兼容性問題
// 禁用包導出配置以避免 "No direct method <init>" 錯誤
config.resolver.unstable_enablePackageExports = false;

// 可選：添加條件名稱配置以進一步提高兼容性
config.resolver.unstable_conditionNames = ["browser"];

// 排除 Node.js 專用包，避免在 React Native 中加載
config.resolver.blockList = [
  /scripts\/.*/,  // 排除所有腳本文件
  /node_modules\/dotenv\/.*/,  // 排除 dotenv 包
];

// 設置平台特定的擴展名解析順序
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config; 