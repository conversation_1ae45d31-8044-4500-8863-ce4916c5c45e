import { useLocalSearchParams } from 'expo-router';
import React from 'react';

import { NotificationDetails } from '@/components/notifications/NotificationDetails';

/**
 * 通知詳情頁面
 * 使用動態路由參數獲取通知ID並顯示詳情
 */
export default function NotificationDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();

  if (!id) {
    throw new Error('通知ID參數缺失');
  }

  return <NotificationDetails notificationId={id} />;
}
