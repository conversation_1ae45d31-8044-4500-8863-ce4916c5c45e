import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState, useCallback, useMemo } from 'react';
import { Alert, Platform, ScrollView, StyleSheet, Text as RNText, View } from 'react-native';
import {
  Appbar,
  Button,
  Surface,
  Text,
  TextInput,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import RecipientsModal from '@/components/RecipientsModal';
import { createAlert } from '@/services/firebaseFunctions';
import { getStaffById, getGroupById } from '@/data/staff';

// Case type options with icons
const CASE_TYPES = [
  {
    id: 'mother_baby',
    label: 'Mother and Baby Transfer',
    shortLabel: 'Mother & Baby',
    icon: 'family-restroom' as const // Material icon for mother and baby
  },
  {
    id: 'mother_only',
    label: 'Mother Only Transfer',
    shortLabel: 'Mother Only',
    icon: 'person' as const // Material icon for person/woman
  },
  {
    id: 'baby_nicu_scbu',
    label: 'Baby Transfer to NICU/SCBU',
    shortLabel: 'Baby to NICU',
    icon: 'child-care' as const // Material icon for baby/child
  },
] as const;

// Notification creation screen - allows users to build specific notification details
export default function InitiateNotificationScreen() {
  // Get current theme to support Dark Mode
  const theme = useTheme();

  // Form state management
  const [selectedCaseType, setSelectedCaseType] = useState<string>('');
  const [motherInitial, setMotherInitial] = useState('');
  const [bedNumber, setBedNumber] = useState('');
  const [ward, setWard] = useState('');
  const [clinicalNotes, setClinicalNotes] = useState('');
  const [isNotesExpanded, setIsNotesExpanded] = useState(false);
  const [selectedRecipients, setSelectedRecipients] = useState<string[]>([]);
  const [showRecipientsModal, setShowRecipientsModal] = useState(false);
  const [isSending, setIsSending] = useState(false);

  // Handle close/back operation - 使用 useCallback 優化性能
  const handleClose = useCallback(() => {
    router.back();
  }, []);

  // Handle case type selection - 使用 useCallback 優化性能
  const handleCaseTypeSelect = useCallback((caseTypeId: string) => {
    setSelectedCaseType(caseTypeId);
  }, []);

  // Handle recipients selection - 使用 useCallback 優化性能
  const handleRecipientsSelect = useCallback(() => {
    setShowRecipientsModal(true);
  }, []);

  // Handle recipients modal dismiss - 使用 useCallback 優化性能
  const handleRecipientsModalDismiss = useCallback(() => {
    setShowRecipientsModal(false);
  }, []);

  // Handle recipients change - 使用 useCallback 優化性能
  const handleRecipientsChange = useCallback((recipients: string[]) => {
    setSelectedRecipients(recipients);
  }, []);

  // Check if required fields are filled - 使用 useMemo 優化性能
  const isFormValid = useMemo(() => 
    selectedCaseType && motherInitial.trim() && bedNumber.trim() && ward.trim(),
    [selectedCaseType, motherInitial, bedNumber, ward]
  );

  // Handle send notification - 使用 useCallback 優化性能並整合 Cloud Functions
  const handleSendNotification = useCallback(async () => {
    if (!isFormValid || isSending) return;
    
    setIsSending(true);
    
    try {
      // 將 case type 轉換為 Cloud Functions 預期的格式
      const caseTypeMapping: Record<string, 'mother_baby_transfer' | 'mother_only_transfer' | 'baby_to_nicu'> = {
        'mother_baby': 'mother_baby_transfer',
        'mother_only': 'mother_only_transfer',
        'baby_nicu_scbu': 'baby_to_nicu',
      };

      // 準備 recipientDeviceIDs（包含個人和群組成員）
      const recipientDeviceIDs: string[] = [];
      
      selectedRecipients.forEach(id => {
        // 檢查是否為群組
        const group = getGroupById(id);
        if (group && group.memberIds) {
          // 如果是群組，添加所有成員的 ID
          recipientDeviceIDs.push(...group.memberIds);
          return;
        }

        // 如果是個人，直接添加
        const staff = getStaffById(id);
        if (staff) {
          recipientDeviceIDs.push(staff.id);
        }
      });

      // 去除重複的 ID
      const uniqueRecipientIDs = [...new Set(recipientDeviceIDs)];

      if (uniqueRecipientIDs.length === 0) {
        Alert.alert('Validation Error', 'Please select at least one recipient');
        return;
      }

      console.log('發送通知...', {
        caseType: caseTypeMapping[selectedCaseType],
        motherInitial,
        bedNumber,
        designatedWard: ward,
        clinicalNotes,
        recipientDeviceIDs: uniqueRecipientIDs,
      });

      // 調用 Cloud Functions
      const result = await createAlert({
        caseType: caseTypeMapping[selectedCaseType],
        motherInitial: motherInitial.trim(),
        bedNumber: bedNumber.trim(),
        designatedWard: ward.trim(),
        clinicalNotes: clinicalNotes.trim() || undefined,
        recipientDeviceIDs: uniqueRecipientIDs,
      });

      if (result.success) {
        Alert.alert(
          'Notification Sent',
          `通知已成功發送！\n事件 ID: ${result.eventID}${
            result.failedRecipients && result.failedRecipients.length > 0
              ? `\n注意：${result.failedRecipients.length} 位接收者發送失敗`
              : ''
          }`,
          [
            {
              text: 'OK',
              onPress: () => {
                // 清空表單並返回主頁
                setSelectedCaseType('');
                setMotherInitial('');
                setBedNumber('');
                setWard('');
                setClinicalNotes('');
                setSelectedRecipients([]);
                router.back();
              },
            },
          ]
        );
      } else {
        Alert.alert('Send Failed', result.message || '發送通知失敗，請稍後重試');
      }
    } catch (error) {
      console.error('發送通知時發生錯誤:', error);
      Alert.alert(
        'Send Failed',
        `發送通知時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}\n請檢查網路連接後重試`
      );
    } finally {
      setIsSending(false);
    }
  }, [selectedCaseType, motherInitial, bedNumber, ward, clinicalNotes, selectedRecipients, isFormValid, isSending]);

  // Get field background color based on completion status - 使用 useCallback 優化性能
  const getFieldBackgroundColor = useCallback((value: string, isRequired: boolean = false) => {
    if (!isRequired) return theme.colors.surface;
    if (value.trim()) {
      return theme.dark ? '#1B4332' : '#D1FAE5'; // Dark green for dark mode, light green for light mode
    }
    return theme.colors.surface;
  }, [theme.colors.surface, theme.dark]);

  // Get selected recipients data for display - 使用 useMemo 優化性能
  const selectedRecipientsData = useMemo(() => {
    const selectedData: { id: string; name: string; initials: string; color: string }[] = [];

    selectedRecipients.forEach(id => {
      // Check if it's a group
      const group = getGroupById(id);
      if (group) {
        selectedData.push({
          id: group.id,
          name: group.name,
          initials: group.initials,
          color: group.color
        });
        return;
      }

      // Check if it's an individual staff member
      const staff = getStaffById(id);
      if (staff) {
        selectedData.push({
          id: staff.id,
          name: staff.name,
          initials: staff.initials,
          color: staff.color
        });
      }
    });

    return selectedData;
  }, [selectedRecipients]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header with close button - 優化高度與主頁面保持一致 */}
      <Appbar.Header 
        style={[styles.header, { backgroundColor: theme.colors.surface }]}
        statusBarHeight={0} // 修復 Modal 中 header 高度與主頁面不一致的問題
      >
        <Appbar.Action
          icon="close"
          onPress={handleClose}
          iconColor={theme.colors.onSurface}
        />
        <Appbar.Content
          title="New Notification"
          titleStyle={[styles.headerTitle, { color: theme.colors.onSurface }]}
        />
      </Appbar.Header>

      {/* Main content area */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        {/* Case type button group - Xiaohongshu style */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Case Type
          </Text>
          <View style={styles.caseTypeButtonGroup}>
            {CASE_TYPES.map((caseType) => (
              <Button
                key={caseType.id}
                mode={selectedCaseType === caseType.id ? 'contained' : 'outlined'}
                onPress={() => handleCaseTypeSelect(caseType.id)}
                style={[
                  styles.caseTypeButton,
                  selectedCaseType === caseType.id && { backgroundColor: theme.colors.primary }
                ]}
                labelStyle={[
                  styles.caseTypeButtonLabel,
                  {
                    color: selectedCaseType === caseType.id
                      ? theme.colors.onPrimary
                      : theme.colors.onSurface
                  }
                ]}
                contentStyle={styles.caseTypeButtonContent}
                icon={({ size }) => (
                  <MaterialIcons
                    name={caseType.icon}
                    size={size}
                    color={selectedCaseType === caseType.id ? theme.colors.onPrimary : theme.colors.onSurface}
                  />
                )}
              >
                {caseType.shortLabel}
              </Button>
            ))}
          </View>
        </View>

        {/* Mother's Details */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Mother&apos;s Details
          </Text>
          <View style={styles.motherDetailsRow}>
            <View style={styles.halfWidthContainer}>
              <View style={styles.labelContainer}>
                <Text style={[styles.inputLabel, { color: theme.colors.onSurface }]}>Initial</Text>
                <Text style={styles.requiredAsterisk}>*</Text>
              </View>
              <TextInput
                value={motherInitial}
                onChangeText={setMotherInitial}
                style={[
                  styles.halfWidthInput,
                  { backgroundColor: getFieldBackgroundColor(motherInitial, true) }
                ]}
                mode="outlined"
                placeholder="Enter initial"
                dense
              />
            </View>
            <View style={styles.halfWidthContainer}>
              <View style={styles.labelContainer}>
                <Text style={[styles.inputLabel, { color: theme.colors.onSurface }]}>Bed Number</Text>
                <Text style={styles.requiredAsterisk}>*</Text>
              </View>
              <TextInput
                value={bedNumber}
                onChangeText={setBedNumber}
                style={[
                  styles.halfWidthInput,
                  { backgroundColor: getFieldBackgroundColor(bedNumber, true) }
                ]}
                mode="outlined"
                placeholder="Enter bed number"
                dense
              />
            </View>
          </View>
        </View>

        {/* Designated Ward */}
        <View style={styles.section}>
          <View style={styles.labelContainer}>
            <Text style={[styles.inputLabel, { color: theme.colors.onSurface }]}>Designated Ward</Text>
            <Text style={styles.requiredAsterisk}>*</Text>
          </View>
          <TextInput
            value={ward}
            onChangeText={setWard}
            style={[
              styles.fullWidthInput,
              { backgroundColor: getFieldBackgroundColor(ward, true) }
            ]}
            mode="outlined"
            placeholder="Enter ward name"
            dense
          />
        </View>

        {/* Clinical Notes */}
        <View style={styles.section}>
          <Button
            mode="outlined"
            onPress={() => setIsNotesExpanded(!isNotesExpanded)}
            style={[styles.expandButton, { borderColor: theme.colors.outline }]}
            contentStyle={styles.expandButtonContent}
            labelStyle={{ color: theme.colors.onSurface }}
            icon={({ size }) => (
              <MaterialIcons
                name={isNotesExpanded ? "expand-less" : "expand-more"}
                size={size}
                color={theme.colors.onSurface}
              />
            )}
          >
            Add Clinical Note (Optional)
          </Button>
          {isNotesExpanded && (
            <TextInput
              value={clinicalNotes}
              onChangeText={setClinicalNotes}
              style={[styles.notesInput, { backgroundColor: getFieldBackgroundColor(clinicalNotes, false) }]}
              mode="outlined"
              placeholder="Enter clinical notes (optional)..."
              multiline
              numberOfLines={8}
              dense
            />
          )}
        </View>

        {/* Recipients */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Recipients
          </Text>
          <View style={styles.recipientsList}>
            {selectedRecipientsData.map((recipient) => (
              <View key={recipient.id} style={styles.recipientItem}>
                <View style={[styles.recipientAvatar, { backgroundColor: recipient.color }]}>
                  <RNText style={styles.recipientInitials}>{recipient.initials}</RNText>
                </View>
                <RNText style={[styles.recipientText, { color: theme.colors.onSurface }]}>
                  {recipient.name}
                </RNText>
              </View>
            ))}
            {selectedRecipientsData.length === 0 && (
              <RNText style={[styles.noRecipientsText, { color: theme.colors.onSurfaceVariant }]}>
                No recipients selected
              </RNText>
            )}
          </View>
          <Button
            mode="outlined"
            onPress={handleRecipientsSelect}
            style={[styles.selectRecipientsButton, { borderColor: theme.colors.outline }]}
            contentStyle={styles.selectRecipientsContent}
            labelStyle={{ color: theme.colors.onSurface }}
            icon={({ size }) => (
              <MaterialIcons name="group-add" size={size} color={theme.colors.onSurface} />
            )}
          >
            Select Recipients
          </Button>
        </View>
      </ScrollView>

      {/* Bottom send button */}
      <Surface style={[styles.bottomContainer, { backgroundColor: theme.colors.surface }]} elevation={4}>
        <Button
          mode="contained"
          onPress={handleSendNotification}
          style={[
            styles.sendButton,
            {
              backgroundColor: (isFormValid && !isSending)
                ? theme.colors.primary
                : theme.dark
                  ? 'rgba(255, 255, 255, 0.12)' // 深色模式下更明顯的背景
                  : 'rgba(0, 0, 0, 0.12)', // 淺色模式下更明顯的背景
              opacity: (isFormValid && !isSending) ? 1 : 0.8 // 提高 disabled 狀態的不透明度
            }
          ]}
          contentStyle={styles.sendButtonContent}
          labelStyle={[
            styles.sendButtonLabel,
            {
              color: (isFormValid && !isSending)
                ? theme.colors.onPrimary
                : theme.dark
                  ? 'rgba(255, 255, 255, 0.6)' // 深色模式下更明顯的文字
                  : 'rgba(0, 0, 0, 0.6)' // 淺色模式下更明顯的文字
            }
          ]}
          disabled={!isFormValid || isSending}
          loading={isSending}
        >
          {isSending ? 'Sending...' : 'Send Notification'}
        </Button>
      </Surface>

      {/* Recipients Selection Modal */}
      <RecipientsModal
        visible={showRecipientsModal}
        onDismiss={handleRecipientsModalDismiss}
        selectedRecipients={selectedRecipients}
        onRecipientsChange={handleRecipientsChange}
      />
    </SafeAreaView>
  );
}

// Style definitions - using theme colors to support Dark Mode and platform-specific fonts
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    // 與主頁面保持一致的 header 樣式
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    height:64,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100, // Leave space for bottom button
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  // Case type button group styles - Vertical layout for better text display
  caseTypeButtonGroup: {
    flexDirection: 'column',
    gap: 12,
  },
  caseTypeButton: {
    borderRadius: 12,
    minHeight: 48,
  },
  caseTypeButtonContent: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  caseTypeButtonLabel: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 18,
      includeFontPadding: false,
    }),
  },
  // Mother's details styles
  motherDetailsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidthContainer: {
    flex: 1,
  },
  halfWidthInput: {
    flex: 1,
  },
  // Label and required field styles
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  requiredAsterisk: {
    color: '#EF4444', // Red color for asterisk
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  fullWidthInput: {
    width: '100%',
  },
  // Clinical notes styles
  expandButton: {
    borderRadius: 12,
    marginBottom: 8,
  },
  expandButtonContent: {
    paddingVertical: 8,
    justifyContent: 'space-between',
    flexDirection: 'row-reverse',
  },
  notesInput: {
    marginTop: 8,
  },
  // Recipients styles
  recipientsList: {
    marginBottom: 16,
  },
  recipientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  recipientAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  recipientInitials: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 16,
      includeFontPadding: false,
    }),
  },
  recipientText: {
    fontSize: 16,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 20,
      includeFontPadding: false,
    }),
  },
  noRecipientsText: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 16,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 18,
      includeFontPadding: false,
    }),
  },
  selectRecipientsButton: {
    borderRadius: 12,
  },
  selectRecipientsContent: {
    paddingVertical: 8,
    justifyContent: 'center',
  },
  // Bottom button styles
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16, // Consider safe area
  },
  sendButton: {
    borderRadius: 12,
    minHeight: 48,
  },
  sendButtonContent: {
    paddingVertical: 12,
  },
  sendButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 20,
      includeFontPadding: false,
    }),
  },
});
