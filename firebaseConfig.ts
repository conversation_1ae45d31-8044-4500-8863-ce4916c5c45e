import { initializeApp, getApps, type FirebaseApp } from 'firebase/app';
import { getAuth, type Auth } from 'firebase/auth';
import { getDatabase, type Database } from 'firebase/database';
import { getFirestore, type Firestore } from 'firebase/firestore';
import { getFunctions, type Functions } from 'firebase/functions';
import { NODE_ENV } from '@env';


const productionConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

const testConfig = {
  apiKey: "AIzaSyDfTg1CV5U_bGR50SuvihQxemS_jZN-bz4",
  authDomain: "qmnoti-test.firebaseapp.com",
  projectId: "qmnoti-test",
  storageBucket: "qmnoti-test.firebasestorage.app",
  messagingSenderId: "175834354502",
  appId: "1:175834354502:web:053bd4096fb906ff4fcb47",
  measurementId: "G-N8CJ8GQWYF",
  databaseURL: "https://qmnoti-test-default-rtdb.asia-southeast1.firebasedatabase.app/"
};


/**
 * 檢查是否為開發環境。
 * 使用 NODE_ENV 環境變數進行環境判斷，支援更靈活的環境配置。
 * - development: 使用測試 Firebase 項目 (qmnoti-test)
 * - production: 使用正式 Firebase 項目 (qmnoti)
 *
 * 備用方案：如果 NODE_ENV 未設置，則使用 __DEV__ 作為備用判斷
 */
const isDevelopment = NODE_ENV === 'development' || (NODE_ENV === undefined && __DEV__);

/**
 * 根據環境選擇 Firebase 配置
 */
const firebaseConfig = isDevelopment ? testConfig : productionConfig;

// 避免重複初始化 Firebase App
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// 初始化 Firebase 服務
const firestoreDB: Firestore = getFirestore(app);
const realtimeDB: Database = getDatabase(app);
const auth: Auth = getAuth(app);
// Functions 使用 asia-east1 區域
const functions: Functions = getFunctions(app, 'asia-east1');

// 環境信息輸出，便於 Debug
console.log(`[FirebaseConfig] App is running in ${isDevelopment ? 'DEVELOPMENT' : 'PRODUCTION'} mode.`);
console.log(`[FirebaseConfig] Project ID: ${firebaseConfig.projectId}`);
console.log(`[FirebaseConfig] Functions Region: asia-east1`);


export { app, auth, firestoreDB, realtimeDB, functions };
