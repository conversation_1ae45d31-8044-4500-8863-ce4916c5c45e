import { initializeApp, getApps, type FirebaseApp } from 'firebase/app';
import { getAuth, type Auth } from 'firebase/auth';
import { getDatabase, type Database } from 'firebase/database';
import { getFirestore, type Firestore } from 'firebase/firestore';
import { getFunctions, type Functions } from 'firebase/functions';


const productionConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

const testConfig = {
  apiKey: "AIzaSyDfTg1CV5U_bGR50SuvihQxemS_jZN-bz4",
  authDomain: "qmnoti-test.firebaseapp.com",
  projectId: "qmnoti-test",
  storageBucket: "qmnoti-test.firebasestorage.app",
  messagingSenderId: "175834354502",
  appId: "1:175834354502:web:053bd4096fb906ff4fcb47",
  measurementId: "G-N8CJ8GQWYF",
  databaseURL: "https://qmnoti-test-default-rtdb.asia-southeast1.firebasedatabase.app/"
};


/**
 * 檢查是否為開發環境。
 * 在 React Native 中，`__DEV__` 是由打包工具 Metro 提供的全域變數。
 * 這是在客戶端判斷環境最可靠的方式。
 * - 開發模式 (npx expo start): __DEV__ === true
 * - 生產打包 (eas build): __DEV__ === false
 */
const isDevelopment = __DEV__;

/**
 * 根據環境選擇 Firebase 配置
 */
const firebaseConfig = isDevelopment ? testConfig : productionConfig;

// 避免重複初始化 Firebase App
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// 初始化 Firebase 服務
const firestoreDB: Firestore = getFirestore(app);
const realtimeDB: Database = getDatabase(app);
const auth: Auth = getAuth(app);
// Functions 使用 asia-east1 區域
const functions: Functions = getFunctions(app, 'asia-east1');

// 環境信息輸出，便於 Debug
console.log(`[FirebaseConfig] App is running in ${isDevelopment ? 'DEVELOPMENT' : 'PRODUCTION'} mode.`);
console.log(`[FirebaseConfig] Project ID: ${firebaseConfig.projectId}`);
console.log(`[FirebaseConfig] Functions Region: asia-east1`);


export { app, auth, firestoreDB, realtimeDB, functions };
