{"expo": {"name": "QMNotiAugment", "slug": "QMNotiAugment", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "qmnotiaugment", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.innospire.qmnoti"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.innospire.qmnoti", "googleServicesFile": "./google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-dev-client", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#ffffff"}], ["@react-native-firebase/app", {"android": {"googleServicesFile": "./google-services.json"}}], ["@react-native-firebase/messaging"]], "experiments": {"typedRoutes": true}}}