import React, { createContext, useContext, useState, useCallback, useMemo, ReactNode } from 'react';
import { Notification, NotificationContextType } from '@/types/notification';
import { getStaffById } from '@/data/staff';
import { NotificationRecipient, RecipientStatus } from '@/types/staff';
import { acknowledgeAlert, cancelAlert } from '@/services/firebaseFunctions';

// 創建通知上下文
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

/**
 * 根據員工ID創建通知接收者
 */
function createNotificationRecipient(
  staffId: string,
  status: RecipientStatus,
  isCurrentUser: boolean = false,
  isInitiator: boolean = false,
  lastUpdated?: Date
): NotificationRecipient | null {
  const staff = getStaffById(staffId);
  if (!staff) return null;

  return {
    ...staff,
    status,
    isCurrentUser,
    isInitiator,
    lastUpdated: lastUpdated || new Date(),
  };
}

// 模擬通知數據 - 基於設計文件的示例，包含接收者信息和臨床筆記
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'mother_baby_transfer',
    title: 'Mother/Baby Transfer',
    description: 'Mother: <PERSON> <PERSON>, Baby: Girl',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小時前
    status: 'pending',
    priority: 'high',
    initiator: {
      id: '2',
      name: '<PERSON>'
    },
    patient: {
      name: 'Jessica Davis',
      bedNumber: 'A301',
      ward: '5B'
    },
    clinicalNotes: 'Patient requires immediate transfer to specialized care unit. Please ensure all medical equipment is ready.',
    recipients: [
      createNotificationRecipient('3', 'confirmed', false, false, new Date(Date.now() - 30 * 60 * 1000)),
      createNotificationRecipient('10', 'received', true, false, new Date(Date.now() - 10 * 60 * 1000))
    ].filter((r): r is NotificationRecipient => r !== null),
    currentUserId: '10'
  },
  {
    id: '2',
    type: 'mother_baby_transfer',
    title: 'Mother/Baby Transfer',
    description: 'Mother: Sophia Brown, Baby: Girl',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 昨天
    status: 'pending',
    priority: 'high',
    initiator: {
      id: '9',
      name: 'Dr. Grace Hall',
      avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBnewFcf7Z1ViEI-DOVLtEqAJfV2pZs34Get9JUj6algN83TwvSjmOdsJbz9tnPCm5B8tBgaJtTL5fjw-j_n-j8SeKCqIVnAhqZSnq1Wrtd59C9ESprBbkZ3Ks34Pew-YLbnzQo_EydE_Da8Yo6VzWut-2LaaJ2SUhFB-nMC6f9i1I16JJtLm1noYbh6NUSgIDGHo5kzd5EAg1cBgy_kW7sxayCrL-0BDfA8tSuMgU3u4uMgm8ifrTwtwlpkCXmWSg4gic4mhZpwvE'
    },
    patient: {
      name: 'Sophia Brown',
      bedNumber: 'B205',
      ward: 'D05'
    },
    clinicalNotes: 'This is an important notification regarding the transfer of Sophia Brown and her baby girl. Please review the details and acknowledge receipt.',
    recipients: [
      createNotificationRecipient('3', 'confirmed', false, false, new Date(Date.now() - 2 * 60 * 60 * 1000)),
      createNotificationRecipient('10', 'received', true, false, new Date(Date.now() - 30 * 60 * 1000)),
      createNotificationRecipient('4', 'pending', false, false, new Date())
    ].filter((r): r is NotificationRecipient => r !== null),
    currentUserId: '10'
  },
  {
    id: '3',
    type: 'medication_administered',
    title: 'Medication Administered',
    description: 'Patient: John Doe - Pain Reliever',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6小時前
    status: 'confirmed',
    priority: 'medium',
    initiator: {
      id: '7',
      name: 'Carol Smith'
    },
    patient: {
      name: 'John Doe',
      bedNumber: 'C102',
      ward: 'ICU'
    },
    clinicalNotes: 'Pain medication administered as prescribed. Patient reported significant improvement in comfort level.',
    recipients: [
      createNotificationRecipient('1', 'confirmed', false, false, new Date(Date.now() - 4 * 60 * 60 * 1000))
    ].filter((r): r is NotificationRecipient => r !== null),
    currentUserId: '10'
  },
  {
    id: '4',
    type: 'lab_results_available',
    title: 'Lab Results Available',
    description: 'Patient: Jane Smith - Blood Test',
    timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000), // 昨天下午
    status: 'confirmed',
    priority: 'medium',
    initiator: {
      id: '8',
      name: 'Michael Wilson'
    },
    patient: {
      name: 'Jane Smith',
      bedNumber: 'D304',
      ward: 'General'
    },
    clinicalNotes: 'Blood test results are now available for review. All values are within normal ranges.',
    recipients: [
      createNotificationRecipient('5', 'confirmed', false, false, new Date(Date.now() - 16 * 60 * 60 * 1000))
    ].filter((r): r is NotificationRecipient => r !== null),
    currentUserId: '10'
  },
  {
    id: '5',
    type: 'discharge_approved',
    title: 'Discharge Approved',
    description: 'Patient: Michael Johnson',
    timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000), // 2天前
    status: 'confirmed',
    priority: 'low',
    initiator: {
      id: '1',
      name: 'Jane Doe'
    },
    patient: {
      name: 'Michael Johnson',
      bedNumber: 'E201',
      ward: 'Recovery'
    },
    clinicalNotes: 'Patient has recovered well and is ready for discharge. Please coordinate with family for pickup.',
    recipients: [
      createNotificationRecipient('6', 'confirmed', false, false, new Date(Date.now() - 46 * 60 * 60 * 1000))
    ].filter((r): r is NotificationRecipient => r !== null),
    currentUserId: '10'
  }
];

/**
 * 通知上下文提供者組件
 * 管理應用中所有通知相關的狀態和操作
 */
export function NotificationProvider({ children }: { children: ReactNode }) {
  // 通知列表狀態
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 計算未讀通知數量 - 使用 useMemo 優化性能
  const unreadCount = useMemo(() => {
    return notifications.filter(notification => notification.status === 'pending').length;
  }, [notifications]);

  // 標記通知為已讀
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, status: 'confirmed' as const }
          : notification
      )
    );
  }, []);

  // 標記通知為未讀
  const markAsUnread = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, status: 'pending' as const }
          : notification
      )
    );
  }, []);

  // 刷新通知列表
  const refreshNotifications = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 模擬 API 調用延遲
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 在實際應用中，這裡會調用 API 獲取最新通知
      // const response = await notificationAPI.getNotifications();
      // setNotifications(response.data);
      
      console.log('通知列表已刷新');
    } catch (err) {
      setError('刷新通知失敗');
      console.error('刷新通知錯誤:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 刪除通知
  const deleteNotification = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
  }, []);

  // 標記所有通知為已讀
  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, status: 'confirmed' as const }))
    );
  }, []);

  // 確認通知（用於接收者）- 整合 Cloud Functions
  const acknowledgeNotification = useCallback(async (notificationId: string) => {
    try {
      // 調用 Cloud Functions 確認通知
      const result = await acknowledgeAlert(notificationId);
      
      if (result.success) {
        // 更新本地狀態
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === notificationId
              ? { ...notification, status: 'confirmed' as const }
              : notification
          )
        );
        console.log('通知確認成功:', result.message);
      } else {
        throw new Error(result.message || '確認通知失敗');
      }
    } catch (error) {
      console.error('確認通知時發生錯誤:', error);
      setError(error instanceof Error ? error.message : '確認通知失敗');
      // 可以選擇顯示錯誤提示給用戶
    }
  }, []);

  // 取消通知（用於發送者）- 整合 Cloud Functions
  const cancelNotification = useCallback(async (notificationId: string) => {
    try {
      // 調用 Cloud Functions 取消通知
      const result = await cancelAlert(notificationId);
      
      if (result.success) {
        // 從本地狀態中移除該通知
        setNotifications(prev =>
          prev.filter(notification => notification.id !== notificationId)
        );
        console.log('通知取消成功:', result.message);
      } else {
        throw new Error(result.message || '取消通知失敗');
      }
    } catch (error) {
      console.error('取消通知時發生錯誤:', error);
      setError(error instanceof Error ? error.message : '取消通知失敗');
      // 可以選擇顯示錯誤提示給用戶
    }
  }, []);

  // 獲取通知詳情
  const getNotificationById = useCallback((notificationId: string) => {
    return notifications.find(notification => notification.id === notificationId);
  }, [notifications]);

  // 上下文值 - 使用 useMemo 避免不必要的重新渲染
  const contextValue = useMemo(() => ({
    // 狀態
    notifications,
    unreadCount,
    loading,
    error,
    // 操作
    markAsRead,
    markAsUnread,
    refreshNotifications,
    deleteNotification,
    markAllAsRead,
    acknowledgeNotification,
    cancelNotification,
    getNotificationById,
  }), [
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAsUnread,
    refreshNotifications,
    deleteNotification,
    markAllAsRead,
    acknowledgeNotification,
    cancelNotification,
    getNotificationById,
  ]);

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

/**
 * 使用通知上下文的 Hook
 * 提供類型安全的上下文訪問
 */
export function useNotifications(): NotificationContextType {
  const context = useContext(NotificationContext);
  
  if (context === undefined) {
    throw new Error('useNotifications 必須在 NotificationProvider 內部使用');
  }
  
  return context;
}
