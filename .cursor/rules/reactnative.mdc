---
description: 
globs: 
alwaysApply: true
---
# General Preferences
- Default to Chinese answers; use Katex for math; use MCP tools; handle limitations with alternatives/citations; use Python for math verification.


# Research & Validation
- Validate complex concepts via external search (e.g., Brave Search), citing sources.
- Proactively gather context (web searches for tech, best practices).
- Seek evidence (docs, PoC) for key decisions.
- Critically evaluate sources and searched solutions (applicability, reliability, side effects), testing code if needed. Avoid blind copy-pasting.


# Project Understanding & Planning
- **Understand Project:** Use tools (`read_file`, `search_files`) to analyze project docs (`README.md`) and code. Research requirements and tech online.
- **Clarify Intent:** Ask clarifying questions (JTBD, constraints, etc.) to fully grasp user needs.
- **Plan First:** Create a step-by-step plan in `Schedule.md` *after* understanding requirements/codebase and *before* coding.
- **Visualize (Optional):** Use Mermaid diagrams for complex flows.


# Design & Architecture
- **Design:** Understand requirements deeply; apply modern paradigms; ensure solution is correct, advanced, feasible, and integrates well.
- **Trade-offs:** Analyze pros/cons of different solutions.


# Code Implementation
- **Principles (New):** KISS, YAGNI, Clean Code.
- **Principles (Modify):** Minimal Change, Boy Scout Rule, Reuse existing patterns.
- **Adherence:** Follow tech specs, project conventions, and understand context before modifying.
- **Preserve History:** Comment out old code instead of deleting.


# Code Quality & Standards
- **Comments:** Add clear Traditional Chinese comments for logic; keep identifiers English.
- **Quality Focus:** Implement type checking, error handling, logging. Address potential issues (e.g., memory leaks). Consider optimization, advanced features, and compatibility.


# Problem Solving & Debugging
- **Analyze:** Understand code flow thoroughly.
- **Identify Root Cause:** Find the origin of the problem.
- **Systematic Approach:** Use methods like DMAIC or 5 Whys if needed.


# Testing
- Write required unit, integration, or UI tests.


# Project Management & Communication
- **Track Progress:** Maintain `Schedule.md` (TO-DO, DOING, DONE); update promptly.
- **Communicate Changes:** Confirm approach, outline changes, get approval for major modifications.
- **Debug Collaboratively:** Communicate with the user during debugging.


# Collaboration & Communication
- **Confirm & Iterate:** Present plans for discussion; adjust flexibly based on user feedback.


# Documentation
- **Maintain Docs:** Create/Update `README.md`, record plan in `Schedule.md`, and document research/details/decisions in `docs/`.
- **Record RCA:** Document Root Cause Analysis when required.
- **Handoff:** Ensure plans/docs are clear.
- **Assist:** Help update `README.md` and review changes.


# Process Improvement
- **Learn & Adapt:** If methods/code are found to be incorrect/suboptimal, suggest revisions in `Schedule.md` and update/generate memories (in English).



