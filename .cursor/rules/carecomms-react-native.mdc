---
description: 
globs: 
alwaysApply: true
---
# CareComms React Native App 开发规范

你是一个专精于 TypeScript、React Native、Expo、Firebase 和医疗应用开发的专家。

## 项目概述

这是一个母嬰轉送即時通知应用程序，允许医院护理师发送关于母婴转送的紧急通知并追踪确认状态。

### 核心技术栈
- **React Native**: 0.79.2 + Expo Router 5.0.7
- **UI组件库**: React Native Paper 5.14.5 (Material Design)
- **包管理器**: pnpm (必须使用)
- **数据库**: Firebase Firestore + Realtime Database
- **推送通知**: Firebase Cloud Messaging (FCM)
- **语言**: TypeScript 5.8.3 (严格模式)

## 代码风格和结构

### 基本原则
- 编写简洁、类型安全的 TypeScript 代码
- 使用函数式组件和 Hooks，避免类组件
- 确保组件模块化、可重用和可维护
- 按功能组织文件，将相关组件、hooks 和样式分组
- 优先考虑安卓端开发和测试

### 文件组织结构
```
app/
├── (tabs)/          # 主要页面路由
├── components/      # 可重用组件
├── hooks/          # 自定义 hooks
├── services/       # Firebase服务和API
├── types/          # TypeScript类型定义
├── utils/          # 工具函数
└── constants/      # 常量定义
```

### 命名约定
- **变量和函数**: camelCase (例如：`isFetchingData`, `handleUserInput`)
- **组件名**: PascalCase (例如：`UserProfile`, `NotificationPanel`)
- **文件名**: kebab-case for directories, PascalCase for components
- **常量**: UPPER_SNAKE_CASE (例如：`NOTIFICATION_TYPES`)

## TypeScript 使用规范

### 类型定义
```typescript
// 优先使用 interface 而非 type
interface NotificationEvent {
  eventId: string;
  initiatorDeviceId: string;
  caseType: 'mother_baby_transfer' | 'mother_only_transfer' | 'baby_to_nicu';
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  notes?: string;
  recipients: Record<string, RecipientStatus>;
  timestampCreated: number;
  status: 'active' | 'cancelled' | 'resolved';
}

interface RecipientStatus {
  nickname: string;
  status: 'send_failed' | 'fcm_sent_pending_ack' | 'acknowledged';
  lastUpdateTimestamp: number;
}
```

### 严格类型检查
- 启用 `tsconfig.json` 中的严格模式
- 避免使用 `any`，追求精确的类型定义
- 使用 `React.FC` 为带 props 的函数组件定义类型
- 为所有组件 props 和 state 使用 interface

## React Native Paper UI 组件使用

### 组件选择原则
```typescript
// 优先使用 Paper 组件而非原生组件
import { Button, Card, TextInput, Portal, Dialog } from 'react-native-paper';

// 示例：通知发送表单
const NotificationForm: React.FC = () => {
  return (
    <Card style={styles.container}>
      <Card.Title title="发起母嬰轉送通知" />
      <Card.Content>
        <TextInput
          label="母親姓名縮寫"
          mode="outlined"
          value={motherInitial}
          onChangeText={setMotherInitial}
        />
        <Button 
          mode="contained" 
          onPress={handleSendNotification}
          style={styles.sendButton}
        >
          發送通知
        </Button>
      </Card.Content>
    </Card>
  );
};
```

### 主题配置
- 遵循 Material Design 3.0 设计规范
- 使用 Paper 的主题系统进行一致的颜色和字体配置
- 确保深色/浅色主题支持

## Firebase 集成规范

### 数据库使用策略

**Firestore (静态数据)**
```typescript
// 用户资料存储
interface User {
  deviceId: string;
  nickname: string;
  fcmToken: string;
  lastSeen: number;
}

// 群组信息存储
interface Group {
  groupId: string;
  name: string;
  members: string[]; // deviceId数组
  createdBy: string;
  createdAt: number;
}
```

**Realtime Database (即时数据)**
```typescript
// 通知事件实时状态
const alertEventsRef = database().ref('alertEvents');
const eventRef = alertEventsRef.child(eventId);

// 监听状态变化
eventRef.on('value', (snapshot) => {
  const eventData = snapshot.val();
  updateRecipientStatus(eventData.recipients);
});
```

### 服务层架构
```typescript
// services/firebase.ts
export class FirebaseService {
  // Firestore 操作
  async registerUser(user: User): Promise<void> {
    await firestore().collection('users').doc(user.deviceId).set(user);
  }

  // Realtime Database 操作
  async createAlert(alertEvent: NotificationEvent): Promise<string> {
    const eventRef = database().ref('alertEvents').push();
    await eventRef.set(alertEvent);
    return eventRef.key!;
  }

  // FCM 通知发送
  async sendNotificationToRecipients(recipients: string[], alertData: any): Promise<void> {
    // Cloud Function 调用逻辑
  }
}
```

## 性能优化规范

### React Native 优化
- 最小化 `useEffect`、`useState` 和渲染方法中的重计算
- 为静态 props 的组件使用 `React.memo()` 防止不必要的重渲染
- 优化 FlatList：使用 `removeClippedSubviews`、`maxToRenderPerBatch`、`windowSize`
- 避免在 `renderItem` 或事件处理器中使用匿名函数

### 数据库性能
```typescript
// 批量操作优化
const batch = firestore().batch();
recipients.forEach(recipientId => {
  const userRef = firestore().collection('users').doc(recipientId);
  batch.update(userRef, { lastNotified: Date.now() });
});
await batch.commit();

// Realtime Database 监听优化
const recipientsRef = database().ref(`alertEvents/${eventId}/recipients`);
recipientsRef.on('child_changed', handleRecipientStatusChange);
```

## 通知系统实现

### FCM 集成
```typescript
// expo-notifications 配置
import * as Notifications from 'expo-notifications';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    priority: Notifications.AndroidNotificationPriority.HIGH,
  }),
});

// 本地重复提醒
const scheduleRecurringNotification = async (alertId: string) => {
  await Notifications.scheduleNotificationAsync({
    content: {
      title: '[緊急] 母嬰轉送通知',
      body: '您有未确认的转送通知，请及时查看',
      data: { alertId },
      sound: true,
      vibrate: [0, 250, 250, 250],
    },
    trigger: {
      seconds: 60,
      repeats: true,
    },
  });
};
```

## 错误处理和日志

### 全局错误边界
```typescript
class NotificationErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 发送错误日志到 Firebase Analytics
    console.error('Notification Error:', error, errorInfo);
  }
}
```

### 网络错误处理
```typescript
const handleApiCall = async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
  try {
    return await apiCall();
  } catch (error) {
    if (error.code === 'unavailable') {
      // 网络不可用，启用离线模式
      showOfflineMessage();
    }
    console.error('API调用失败:', error);
    return null;
  }
};
```

## 包管理规范

### pnpm 使用
```bash
# 安装依赖
pnpm install

# 添加依赖
pnpm add react-native-paper
pnpm add -D @types/react-native

# 启动开发服务器
pnpm start

# 类型检查
pnpm type-check

# 代码检查
pnpm lint
```

## 测试策略

### 单元测试
```typescript
// __tests__/NotificationService.test.ts
import { FirebaseService } from '../services/firebase';

describe('NotificationService', () => {
  it('应该正确创建通知事件', async () => {
    const service = new FirebaseService();
    const alertEvent = createMockAlertEvent();
    const eventId = await service.createAlert(alertEvent);
    expect(eventId).toBeDefined();
  });
});
```

### 集成测试重点
- 完整的通知发送和接收流程
- 多用户场景测试
- 群组通知功能
- 事件取消功能
- 网络中断恢复测试

## 安全性要求

### 数据保护
- 使用 Firebase Security Rules 保护用户数据
- 敏感信息（如医疗记录）需要加密存储
- 实现适当的用户权限验证

### 网络安全
```typescript
// Firebase Security Rules 示例
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    match /alertEvents/{eventId} {
      allow read: if resource.data.recipients[request.auth.uid] != null;
      allow write: if request.auth.uid == resource.data.initiatorDeviceId;
    }
  }
}
```

## 关键约定

1. **优先安卓开发**: 所有功能优先在安卓平台实现和测试
2. **数据库双重策略**: Firestore存储静态数据，Realtime Database处理实时状态
3. **群组功能优先**: 早期实现群组管理和群组通知功能
4. **离线支持**: 实现基本的离线模式和数据同步
5. **医疗级可靠性**: 确保通知传递的可靠性和状态追踪的准确性
6. **电池优化**: 合理使用后台任务，避免过度消耗电池
7. **多语言支持**: 代码注释使用中文，但保持标识符为英文

## 参考文档

- @React Native Paper 文档
- @Firebase for React Native 文档
- @Expo Notifications 文档
- 项目设计文档: @design.html
- 项目计划: @Schedule.md
- 项目说明: @README.md
