/**
 * Firebase 配置驗證服務
 * 用於檢查 Firebase 配置是否正確，並提供診斷信息
 */

import * as Device from 'expo-device';
import * as Constants from 'expo-constants';
import { app } from '../firebaseConfig';

export interface FirebaseValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  info: string[];
}

/**
 * 驗證 Firebase 配置
 */
export const validateFirebaseConfig = async (): Promise<FirebaseValidationResult> => {
  const result: FirebaseValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    info: []
  };

  try {
    // 檢查 Firebase App 初始化
    if (!app) {
      result.errors.push('Firebase App 未正確初始化');
      result.isValid = false;
    } else {
      result.info.push('Firebase App 初始化成功');
    }

    // 檢查設備環境
    if (!Device.isDevice) {
      result.warnings.push('當前運行在模擬器環境，FCM 功能可能受限');
    } else {
      result.info.push(`運行在真實設備：${Device.osName} ${Device.osVersion}`);
    }

    // 檢查項目配置
    const projectId = (Constants as any)?.expoConfig?.extra?.eas?.projectId ??
                     (Constants as any)?.easConfig?.projectId ??
                     'qmnoti';
    
    if (!projectId || projectId === 'qmnoti') {
      result.warnings.push('使用默認項目 ID，可能影響推播通知功能');
    } else {
      result.info.push(`項目 ID: ${projectId}`);
    }

    // 檢查 React Native Firebase 可用性
    try {
      const messaging = require('@react-native-firebase/messaging');
      if (messaging) {
        result.info.push('React Native Firebase Messaging 可用');
      }
    } catch (rnfError) {
      result.warnings.push('React Native Firebase 不可用，將使用 Expo Push Token');
    }

    // 檢查 Expo Notifications 可用性
    try {
      const Notifications = require('expo-notifications');
      if (Notifications) {
        result.info.push('Expo Notifications 可用');
      }
    } catch (expoError) {
      result.errors.push('Expo Notifications 不可用');
      result.isValid = false;
    }

    // 檢查 Google Services 配置（Android）
    if (Device.osName === 'Android') {
      // 這裡可以添加更多 Android 特定的檢查
      result.info.push('Android 環境檢測完成');
    }

    // 檢查 iOS 配置
    if (Device.osName === 'iOS') {
      // 這裡可以添加更多 iOS 特定的檢查
      result.info.push('iOS 環境檢測完成');
    }

  } catch (error) {
    result.errors.push(`配置驗證過程中發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`);
    result.isValid = false;
  }

  return result;
};

/**
 * 獲取 Firebase 診斷信息
 */
export const getFirebaseDiagnostics = async (): Promise<string> => {
  const validation = await validateFirebaseConfig();
  
  let diagnostics = '=== Firebase 配置診斷 ===\n\n';
  
  diagnostics += `狀態: ${validation.isValid ? '✅ 正常' : '❌ 有問題'}\n\n`;
  
  if (validation.errors.length > 0) {
    diagnostics += '🔴 錯誤:\n';
    validation.errors.forEach(error => {
      diagnostics += `  - ${error}\n`;
    });
    diagnostics += '\n';
  }
  
  if (validation.warnings.length > 0) {
    diagnostics += '🟡 警告:\n';
    validation.warnings.forEach(warning => {
      diagnostics += `  - ${warning}\n`;
    });
    diagnostics += '\n';
  }
  
  if (validation.info.length > 0) {
    diagnostics += 'ℹ️ 信息:\n';
    validation.info.forEach(info => {
      diagnostics += `  - ${info}\n`;
    });
    diagnostics += '\n';
  }
  
  diagnostics += '=== 建議 ===\n';
  if (!validation.isValid) {
    diagnostics += '- 請檢查 Firebase 配置文件\n';
    diagnostics += '- 確保所有必要的依賴已安裝\n';
    diagnostics += '- 在真實設備上測試推播通知功能\n';
  } else {
    diagnostics += '- 配置看起來正常，可以進行功能測試\n';
  }
  
  return diagnostics;
};

/**
 * 簡化的配置檢查，用於快速驗證
 */
export const isFirebaseConfigValid = async (): Promise<boolean> => {
  try {
    const validation = await validateFirebaseConfig();
    return validation.isValid;
  } catch (error) {
    console.error('Firebase 配置檢查失敗:', error);
    return false;
  }
};
