/**
 * Local Data Storage Service
 * Uses AsyncStorage for persistent user profile storage
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserProfile } from '@/types/profile';

/**
 * Storage keys constants
 */
const STORAGE_KEYS = {
  USER_PROFILE: '@user_profile',
} as const;

/**
 * Local storage service class
 */
export class StorageService {
  /**
   * Save user profile
   * @param profile User profile object
   */
  static async saveUserProfile(profile: UserProfile): Promise<void> {
    try {
      const profileWithTimestamp = {
        ...profile,
        updatedAt: Date.now(),
      };
      
      const jsonValue = JSON.stringify(profileWithTimestamp);
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, jsonValue);
      console.log('User profile saved to local storage');
    } catch (error) {
      console.error('Failed to save user profile:', error);
      throw new Error('Failed to save user profile');
    }
  }

  /**
   * Get user profile
   * @returns User profile object or null
   */
  static async getUserProfile(): Promise<UserProfile | null> {
    try {
      const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.USER_PROFILE);
      
      if (jsonValue === null) {
        console.log('No local user profile found');
        return null;
      }
      
      const profile = JSON.parse(jsonValue) as UserProfile;
      console.log('Successfully retrieved local user profile');
      return profile;
    } catch (error) {
      console.error('Failed to get user profile:', error);
      return null;
    }
  }

  /**
   * Delete user profile
   */
  static async deleteUserProfile(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_PROFILE);
      console.log('User profile deleted from local storage');
    } catch (error) {
      console.error('Failed to delete user profile:', error);
      throw new Error('Failed to delete user profile');
    }
  }

  /**
   * Check if user profile exists
   * @returns Whether user profile exists
   */
  static async hasUserProfile(): Promise<boolean> {
    try {
      const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.USER_PROFILE);
      return jsonValue !== null;
    } catch (error) {
      console.error('Failed to check user profile existence:', error);
      return false;
    }
  }

  /**
   * Clear all storage data (for testing and reset)
   */
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.clear();
      console.log('All local storage data cleared');
    } catch (error) {
      console.error('Failed to clear storage data:', error);
      throw new Error('Failed to clear storage data');
    }
  }
} 