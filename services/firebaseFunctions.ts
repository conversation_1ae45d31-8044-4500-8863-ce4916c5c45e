/**
 * Firebase Functions 服務
 * 用於調用 Cloud Functions 中定義的 registerUser 和 createAlert 函數
 */

import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebaseConfig';
import { Platform } from 'react-native';
import * as Device from 'expo-device';
// 直接在文件頂部導入，明確專案依賴
import messaging from '@react-native-firebase/messaging';

// Functions 實例已從 firebaseConfig 導入

/**
 * 環境檢測工具函數
 */
export const debugEnvironment = () => {
  const envInfo = {
    nodeEnv: process.env.NODE_ENV,
    platform: Platform.OS,
    isDevice: Device.isDevice,
    deviceName: Device.deviceName,
    osName: Device.osName,
    osVersion: Device.osVersion,
    firebaseRegion: 'asia-east1',
    timestamp: new Date().toISOString()
  };
  
  console.log('🔍 環境檢測信息:', JSON.stringify(envInfo, null, 2));
  
  // 檢查Firebase Functions是否正確配置
  if (functions) {
    console.log('✅ Firebase Functions實例已配置');
    console.log(`📍 Functions區域: ${functions.region || 'default'}`);
  } else {
    console.error('❌ Firebase Functions實例未配置');
  }
  
  return envInfo;
};

// 類型定義，對應 Cloud Functions 中的類型
interface RegisterUserRequest {
  deviceID: string;
  nickname: string;
  fcmToken: string;
  name?: string;
  role?: string;
  initials?: string;
  color?: string;
  phoneNumber?: string;
  avatar?: string;
}

interface RegisterUserResponse {
  success: boolean;
  message: string;
  user?: any;
}

interface CreateAlertRequest {
  initiatorDeviceID: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  recipientDeviceIDs: string[];
}

interface CreateAlertResponse {
  success: boolean;
  message: string;
  eventID?: string;
  failedRecipients?: { deviceID: string; reason: string }[];
}

// 工具函數：獲取設備唯一 ID
export const getDeviceID = async (): Promise<string> => {
  try {
    // 優先使用設備的唯一識別符
    if (Device.deviceName && Device.osName && Device.osVersion) {
      return `${Device.osName}_${Device.deviceName}_${Device.osVersion}`.replace(/[^a-zA-Z0-9_-]/g, '_');
    }
    
    // 如果無法獲取設備信息，使用當前時間戳和隨機數生成
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `device_${timestamp}_${random}`;
  } catch (error) {
    console.error('生成設備 ID 時發生錯誤:', error);
    // 備用方案：使用時間戳和隨機數
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `device_${timestamp}_${random}`;
  }
};

/**
 * 獲取設備的原生 FCM Token。
 * 此函數專為 Bare Workflow 設計，並直接依賴 @react-native-firebase/messaging。
 * 它移除了對 expo-notifications 的錯誤回退邏輯。
 */
export const getFCMToken = async (): Promise<string> => {
  console.log('開始獲取原生 FCM Token...');

  // 檢查是否為模擬器環境
  const isEmulatorEnv = process.env.NODE_ENV === 'development';
  
  // 1. 檢查是否在真實設備上
  // 在模擬器/模擬器上，無法獲取真實 FCM Token
  if (!Device.isDevice) {
    const simulatorToken = `simulator_fcm_token_${Date.now()}`;
    console.warn(`正在模擬器上運行，將返回一個模擬的 FCM Token: ${simulatorToken}`);
    return simulatorToken;
  }

  // 2. 真實設備在模擬器環境下的特殊處理
  if (isEmulatorEnv && Device.isDevice) {
    console.log('🔧 檢測到真實設備在模擬器環境下運行');
    
    try {
      // 首先嘗試獲取真實的FCM Token
      const authStatus = await messaging().requestPermission();
      const hasPermission =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (hasPermission) {
        console.log('✅ 推播通知權限已獲得，嘗試獲取真實FCM Token...');
        const realToken = await messaging().getToken();
        
        if (realToken && realToken.length > 20) {
          console.log('✅ 成功獲取真實FCM Token (模擬器環境):', realToken.substring(0, 20) + '...');
          return realToken;
        }
      }
    } catch (error) {
      console.warn('⚠️ 在模擬器環境下無法獲取真實FCM Token:', error);
    }
    
    // 如果無法獲取真實FCM Token，生成一個測試用的Token
    const testToken = `test_fcm_token_${Device.osName}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    console.log(`🧪 模擬器環境：為真實設備生成測試FCM Token: ${testToken}`);
    return testToken;
  }
  
  // 3. 真實設備在生產環境下的標準處理
  console.log('🚀 生產環境：正常獲取FCM Token...');
  
  // 請求推播通知權限
  const authStatus = await messaging().requestPermission();
  const hasPermission =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (!hasPermission) {
    const noPermissionToken = `no_permission_token_${Date.now()}`;
    console.warn(`用戶拒絕了推播通知權限！返回一個無權限的標識 Token: ${noPermissionToken}`);
    return noPermissionToken;
  }
  
  console.log('推播通知權限已獲得。');

  // 獲取原生的 FCM Token
  try {
    console.log('嘗試透過 @react-native-firebase/messaging 獲取 Token...');
    const fcmToken = await messaging().getToken();
    
    if (fcmToken) {
        console.log('✅ 成功獲取原生 FCM Token:', fcmToken);
        return fcmToken;
    } else {
        console.error('❌ messaging().getToken() 返回了空值！');
        throw new Error('無法獲取 FCM Token，Firebase 返回為空。');
    }

  } catch (error) {
    console.error('❌ 獲取原生 FCM Token 失敗! 這通常表示原生設定有誤。', error);
    throw new Error('無法獲取 FCM Token。請檢查 app.config.js 的 plugins 並執行 npx expo prebuild --clean。');
  }
};

/**
 * 註冊用戶 Cloud Function
 */
export const registerUser = async (userData: Omit<RegisterUserRequest, 'deviceID' | 'fcmToken'>): Promise<RegisterUserResponse> => {
  try {
    // 調試環境信息
    const envInfo = debugEnvironment();
    console.log('🔧 開始註冊用戶流程...');
    
    // 獲取設備 ID 和 FCM Token
    console.log('📱 獲取設備 ID...');
    const deviceID = await getDeviceID();
    console.log(`✅ 設備 ID: ${deviceID}`);
    
    console.log('🔔 獲取 FCM Token...');
    const fcmToken = await getFCMToken();
    console.log(`✅ FCM Token (前20字符): ${fcmToken.substring(0, 20)}...`);

    const requestData = {
      deviceID,
      fcmToken,
      ...userData,
    };
    
    console.log('📤 準備調用 registerUser Cloud Function...');
    console.log('📋 請求數據 (隱藏Token):', {
      ...requestData,
      fcmToken: `${fcmToken.substring(0, 10)}...(隱藏)`
    });

    const registerUserFunction = httpsCallable<RegisterUserRequest, RegisterUserResponse>(functions, 'registerUser');
    
    const result = await registerUserFunction(requestData);

    console.log('✅ registerUser Cloud Function 調用成功');
    console.log('📥 響應數據:', result.data);

    return result.data;
  } catch (error) {
    console.error('❌ 註冊用戶時發生錯誤:', error);
    
    // 提供更詳細的錯誤信息
    if (error && typeof error === 'object') {
      const errorObj = error as any;
      if (errorObj.code) {
        console.error(`🔴 Firebase錯誤代碼: ${errorObj.code}`);
      }
      if (errorObj.message) {
        console.error(`🔴 錯誤訊息: ${errorObj.message}`);
      }
      if (errorObj.details) {
        console.error(`🔴 錯誤詳情:`, errorObj.details);
      }
    }
    
    throw new Error(`註冊失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 創建通知事件 Cloud Function
 */
export const createAlert = async (alertData: Omit<CreateAlertRequest, 'initiatorDeviceID'>): Promise<CreateAlertResponse> => {
  try {
    // 獲取發起者的設備 ID
    const initiatorDeviceID = await getDeviceID();

    const createAlertFunction = httpsCallable<CreateAlertRequest, CreateAlertResponse>(functions, 'createAlert');
    
    const result = await createAlertFunction({
      initiatorDeviceID,
      ...alertData,
    });

    return result.data;
  } catch (error) {
    console.error('創建通知事件時發生錯誤:', error);
    throw new Error(`發送通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 確認通知 Cloud Function
 */
export const acknowledgeAlert = async (eventID: string): Promise<any> => {
  try {
    const recipientDeviceID = await getDeviceID();

    const acknowledgeAlertFunction = httpsCallable(functions, 'acknowledgeAlert');
    
    const result = await acknowledgeAlertFunction({
      eventID,
      recipientDeviceID,
    });

    return result.data;
  } catch (error) {
    console.error('確認通知時發生錯誤:', error);
    throw new Error(`確認通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 取消通知 Cloud Function
 */
export const cancelAlert = async (eventID: string): Promise<any> => {
  try {
    const initiatorDeviceID = await getDeviceID();

    const cancelAlertFunction = httpsCallable(functions, 'cancelAlert');
    
    const result = await cancelAlertFunction({
      eventID,
      initiatorDeviceID,
    });

    return result.data;
  } catch (error) {
    console.error('取消通知時發生錯誤:', error);
    throw new Error(`取消通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
}; 