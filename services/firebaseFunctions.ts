/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

/**
 * QMNotiAugment 母嬰轉送通知系統 Cloud Functions
 * * 實現功能：
 * 1. registerUser - 用戶註冊與更新
 * 2. createAlert - 創建通知事件並發送 FCM
 * 3. acknowledgeAlert - 接收者確認通知
 * 4. cancelAlert - 發起者取消通知
 * 5. initializeStaffData - 初始化員工數據
 */

import {onCall, HttpsError, CallableRequest} from "firebase-functions/v2/https";
import {onDocumentCreated} from "firebase-functions/v2/firestore";
import * as logger from "firebase-functions/logger";
import {setGlobalOptions} from "firebase-functions/v2";

// Firebase Admin SDK 導入
import {initializeApp, App} from "firebase-admin/app";
import {getFirestore, Firestore, FieldValue} from "firebase-admin/firestore";
import {getDatabase, Database} from "firebase-admin/database";
import {getMessaging, Messaging} from "firebase-admin/messaging";

// =================================================================
// 全域設定與初始化
// =================================================================

setGlobalOptions({region: "asia-east1"});

// 全域變數，用於延遲初始化
let app: App | undefined;
let firestore: Firestore | undefined;
let realtimeDb: Database | undefined;
let messaging: Messaging | undefined;

// 環境變數配置
const isDevelopment = process.env.NODE_ENV === 'development';
const firestoreCollections = {
  users: process.env.FIRESTORE_USERS_COLLECTION || 'users',
  alertEvents: process.env.FIRESTORE_EVENTS_COLLECTION || 'alertEvents',
  groups: process.env.FIRESTORE_GROUPS_COLLECTION || 'groups',
  staff: process.env.FIRESTORE_STAFF_COLLECTION || 'staff'
};
const realtimePaths = {
  presence: process.env.REALTIME_PRESENCE_PATH || 'presence',
  alertEvents: process.env.REALTIME_EVENTS_PATH || 'alertEvents',
  stats: process.env.REALTIME_STATS_PATH || 'stats'
};


/**
 * 延遲初始化並獲取 Firebase 服務。
 * 這種模式可以避免在函式冷啟動時每次都重新初始化，提升效能。
 */
function getFirebaseServices() {
  if (!app) {
    logger.info("Performing first-time initialization of Firebase Admin SDK...");
    if (isDevelopment) {
      logger.info("🧪 開發環境已檢測，將使用測試集合和路徑");
    } else {
      logger.info("🚀 生產環境已檢測，將使用正式集合和路徑");
    }
    logger.info("📊 集合配置:", firestoreCollections);
    logger.info("📈 Realtime Database 路徑:", realtimePaths);
    
    app = initializeApp();
    firestore = getFirestore(app);
    realtimeDb = getDatabase(app);
    messaging = getMessaging(app);
    logger.info("Firebase Admin SDK initialized successfully.");
  } else {
    // 如果已經初始化，直接獲取服務實例
    firestore = getFirestore();
    realtimeDb = getDatabase();
    messaging = getMessaging();
  }
  return {firestore, realtimeDb, messaging, app};
}


// =================================================================
// 類型定義
// =================================================================
interface RegisterUserRequest {
  deviceID: string;
  nickname: string;
  fcmToken: string;
  name?: string;
  role?: string;
  initials?: string;
  color?: string;
  phoneNumber?: string;
  avatar?: string;
}

interface CreateAlertRequest {
  initiatorDeviceID: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  recipientDeviceIDs: string[];
}

interface AcknowledgeAlertRequest {
  eventID: string;
  recipientDeviceID: string;
}

interface CancelAlertRequest {
  eventID: string;
  initiatorDeviceID: string;
}

// =================================================================
// 工具函數
// =================================================================
function generateEventID(): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `event_${timestamp}_${randomStr}`;
}

function isValidFCMToken(token: string): boolean {
  try {
    if (!token || typeof token !== "string" || token.length === 0) {
      logger.warn('FCM Token為空或無效類型');
      return false;
    }
    if (token.startsWith('expo_error_token_') || token.startsWith('error_token_') || token.includes('_error_')) {
      logger.warn(`檢測到錯誤標識FCM Token: ${token.substring(0, 20)}...`);
      return false;
    }
    if (isDevelopment) {
      if (token.startsWith('test_fcm_token_') || token.startsWith('simulator_fcm_token_') || token.startsWith('no_permission_token_')) {
        logger.info(`開發環境：接受測試FCM Token: ${token.substring(0, 20)}...`);
        return true;
      }
      if (token.length > 100 && !token.includes('test_') && !token.includes('simulator_')) {
        logger.info(`開發環境：接受真實設備FCM Token: ${token.substring(0, 20)}... (長度: ${token.length})`);
        return true;
      }
    }
    if (token.length < 20) {
      logger.warn(`FCM Token長度過短: ${token.length}`);
      return false;
    }
    if (!isDevelopment && (token.startsWith('test_') || token.startsWith('simulator_') || token.startsWith('no_permission_'))) {
      logger.warn(`生產環境不接受測試FCM Token: ${token.substring(0, 20)}...`);
      return false;
    }
    const hasInvalidChars = /[<>{}[\]\\|`~!@#$%^&*()+=;'"?/\s]/.test(token);
    if (hasInvalidChars) {
      logger.warn('FCM Token包含明顯無效字符');
      return false;
    }
    logger.info(`FCM Token驗證通過: ${token.substring(0, 20)}... (長度: ${token.length})`);
    return true;
  } catch (error) {
    logger.error('FCM Token驗證過程中發生錯誤:', error);
    return false;
  }
}

function buildFCMPayload(alertData: any): any {
  const caseTypeLabels: Record<string, string> = {
    "mother_baby_transfer": "母親與嬰兒轉送",
    "mother_only_transfer": "僅母親轉送",
    "baby_to_nicu": "嬰兒轉送至 NICU",
  };

  const title = "[緊急] 母嬰轉送通知";
  const caseLabel = caseTypeLabels[alertData.caseType] || alertData.caseType;
  const body = `個案: ${caseLabel}\n母親: ${alertData.motherInitial}${
    alertData.bedNumber ? `\n床號: ${alertData.bedNumber}` : ""
  }\n目標: ${alertData.designatedWard}\n請確認收到`;

  return {
    notification: { title, body },
    data: {
      eventID: alertData.eventID,
      caseType: alertData.caseType,
      motherInitial: alertData.motherInitial,
      bedNumber: alertData.bedNumber || "",
      designatedWard: alertData.designatedWard,
      clinicalNotes: alertData.clinicalNotes || "",
      initiatorNickname: alertData.initiatorNickname,
    },
    android: {
      priority: "high" as const,
      notification: {
        channelId: "maternal_alerts",
        priority: "high" as const,
        sound: "default",
        vibrationPattern: [0, 250, 250, 250],
      },
    },
    apns: {
      headers: { "apns-priority": "10" },
      payload: { aps: { alert: { title, body }, sound: "default", badge: 1 } },
    },
  };
}

// =================================================================
// Cloud Functions 實現
// =================================================================

/**
 * 註冊或更新使用者資訊。
 */
export const registerUser = onCall(async (request: CallableRequest<RegisterUserRequest>) => {
  try {
    const { firestore, realtimeDb } = getFirebaseServices();
    logger.info("開始處理用戶註冊請求");
    const data = request.data;

    if (!data.deviceID || !data.nickname || !data.fcmToken) {
      throw new HttpsError("invalid-argument", "deviceID, nickname, 和 fcmToken 為必填欄位");
    }
    if (!isValidFCMToken(data.fcmToken)) {
      throw new HttpsError("invalid-argument", "無效的 FCM Token");
    }

    const serverTimestamp = FieldValue.serverTimestamp();
    const userData = {
      deviceID: data.deviceID, nickname: data.nickname, fcmToken: data.fcmToken,
      name: data.name || "", role: data.role || "", initials: data.initials || "",
      color: data.color || "#3B82F6", phoneNumber: data.phoneNumber || "", avatar: data.avatar || "",
      lastSeen: serverTimestamp, isActive: true, createdAt: serverTimestamp,
    };

    await firestore!.collection(firestoreCollections.users).doc(data.deviceID).set(userData, {merge: true});
    await realtimeDb!.ref(`${realtimePaths.presence}/${data.deviceID}`).set({ isOnline: true, lastSeen: Date.now(), fcmTokenActive: true });

    logger.info(`用戶註冊成功: ${data.deviceID} (${data.nickname})`);
    return { success: true, message: "用戶註冊成功", user: userData };
  } catch (error) {
    logger.error("用戶註冊錯誤:", error);
    if (error instanceof HttpsError) throw error;
    let errorMessage = "用戶註冊失敗";
    if (error instanceof Error) {
        if (error.message.includes('permission')) errorMessage = "權限錯誤：無法訪問Firebase服務";
        else if (error.message.includes('network') || error.message.includes('timeout')) errorMessage = "網絡錯誤：無法連接到Firebase服務";
        else if (error.message.includes('emulator')) errorMessage = "模擬器連接錯誤：請確認Firebase模擬器正在運行";
        else errorMessage = `用戶註冊失敗: ${error.message}`;
    }
    throw new HttpsError("internal", errorMessage);
  }
});


/**
 * 創建一個新的通知事件。
 * **注意**: 為了測試目的，暫時移除了身份驗證。
 */
export const createAlert = onCall(async (request: CallableRequest<CreateAlertRequest>) => {
  // if (!request.auth) {
  //   logger.error("createAlert 呼叫失敗: 未經授權的使用者。");
  //   throw new HttpsError("unauthenticated", "此操作需要使用者登入。");
  // }
  
  try {
    const { firestore, realtimeDb, messaging } = getFirebaseServices();
    const data = request.data;
    
    if (!data.initiatorDeviceID || !data.caseType || !data.motherInitial || !data.designatedWard || !data.recipientDeviceIDs || data.recipientDeviceIDs.length === 0) {
      throw new HttpsError("invalid-argument", "必填欄位不完整或接收者列表為空");
    }

    const initiatorDoc = await firestore!.collection(firestoreCollections.users).doc(data.initiatorDeviceID).get();
    if (!initiatorDoc.exists) throw new HttpsError("not-found", "發起者用戶不存在");

    const initiator = initiatorDoc.data()!;
    const eventID = generateEventID();
    const serverTimestamp = FieldValue.serverTimestamp();
    const timestamp = Date.now();

    const alertEvent = {
      eventID, initiatorDeviceID: data.initiatorDeviceID, initiatorNickname: initiator.nickname,
      caseType: data.caseType, motherInitial: data.motherInitial, bedNumber: data.bedNumber || "",
      designatedWard: data.designatedWard, clinicalNotes: data.clinicalNotes || "",
      createdAt: serverTimestamp, status: "active", totalRecipients: data.recipientDeviceIDs.length,
      acknowledgedCount: 0,
    };

    await firestore!.collection(firestoreCollections.alertEvents).doc(eventID).set(alertEvent);

    const realtimeEventData: any = {
      initiatorDeviceID: data.initiatorDeviceID, initiatorNickname: initiator.nickname,
      caseType: data.caseType, motherInitial: data.motherInitial, bedNumber: data.bedNumber || "",
      designatedWard: data.designatedWard, clinicalNotes: data.clinicalNotes || "",
      timestampCreated: timestamp, status: "active", recipients: {},
    };

    const failedRecipients: {deviceID: string; reason: string}[] = [];
    const fcmTokens: string[] = [];
    const recipients: Record<string, any> = {};

    for (const recipientID of data.recipientDeviceIDs) {
      try {
        const recipientDoc = await firestore!.collection(firestoreCollections.users).doc(recipientID).get();
        if (!recipientDoc.exists) {
          failedRecipients.push({ deviceID: recipientID, reason: "用戶不存在" });
          recipients[recipientID] = { nickname: "未知用戶", status: "send_failed", lastUpdateTimestamp: timestamp, errorMessage: "用戶不存在" };
          continue;
        }
        const recipient = recipientDoc.data()!;
        if (!recipient.fcmToken || !isValidFCMToken(recipient.fcmToken)) {
          failedRecipients.push({ deviceID: recipientID, reason: "無效的 FCM Token" });
          recipients[recipientID] = { nickname: recipient.nickname, status: "send_failed", lastUpdateTimestamp: timestamp, errorMessage: "無效的 FCM Token" };
          continue;
        }
        fcmTokens.push(recipient.fcmToken);
        recipients[recipientID] = { nickname: recipient.nickname, status: "fcm_sent_pending_ack", fcmTokenUsed: recipient.fcmToken, lastUpdateTimestamp: timestamp };
      } catch (error) {
        logger.error(`處理接收者 ${recipientID} 時發生錯誤:`, error);
        failedRecipients.push({ deviceID: recipientID, reason: "處理用戶資料時發生錯誤" });
        recipients[recipientID] = { nickname: "處理錯誤", status: "send_failed", lastUpdateTimestamp: timestamp, errorMessage: "處理用戶資料時發生錯誤" };
      }
    }

    realtimeEventData.recipients = recipients;
    await realtimeDb!.ref(`${realtimePaths.alertEvents}/${eventID}`).set(realtimeEventData);

    if (fcmTokens.length > 0) {
      if (isDevelopment) {
        logger.info(`開發環境：跳過 FCM 發送，模擬 ${fcmTokens.length} 個通知發送成功`);
      } else {
        try {
          const payload = buildFCMPayload({ ...alertEvent, eventID });
          const response = await messaging!.sendEachForMulticast({ tokens: fcmTokens, ...payload });
          logger.info(`FCM 發送結果: 成功 ${response.successCount}, 失敗 ${response.failureCount}`);
          if (response.failureCount > 0) {
            for (let i = 0; i < response.responses.length; i++) {
              if (!response.responses[i].success) {
                const failedToken = fcmTokens[i];
                for (const [recipientID, recipientData] of Object.entries(recipients)) {
                  if ((recipientData as any).fcmTokenUsed === failedToken) {
                    const errorPath = `${realtimePaths.alertEvents}/${eventID}/recipients/${recipientID}`;
                    await realtimeDb!.ref(errorPath).update({ status: "send_failed", errorMessage: response.responses[i].error?.message || "FCM 發送失敗"});
                  }
                }
              }
            }
          }
        } catch (fcmError) {
          logger.error("FCM 發送錯誤:", fcmError);
          for (const [recipientID, recipientData] of Object.entries(recipients)) {
            if ((recipientData as any).status === "fcm_sent_pending_ack") {
              await realtimeDb!.ref(`${realtimePaths.alertEvents}/${eventID}/recipients/${recipientID}`).update({ status: "send_failed" });
            }
          }
        }
      }
    }
    logger.info(`通知事件創建成功: ${eventID}`);
    return { success: true, message: "通知事件創建成功", eventID, failedRecipients: failedRecipients.length > 0 ? failedRecipients : undefined };
  } catch (error) {
    logger.error("創建通知事件錯誤:", error);
    if (error instanceof HttpsError) throw error;
    throw new HttpsError("internal", "創建通知事件失敗");
  }
});

/**
 * 確認收到一個通知。
 * **注意**: 為了測試目的，暫時移除了身份驗證。
 */
export const acknowledgeAlert = onCall(async (request: CallableRequest<AcknowledgeAlertRequest>) => {
  // if (!request.auth) {
  //   logger.error("acknowledgeAlert 呼叫失敗: 未經授權的使用者。");
  //   throw new HttpsError("unauthenticated", "此操作需要使用者登入。");
  // }

  try {
    const { firestore, realtimeDb } = getFirebaseServices();
    const data = request.data;
    
    if (!data.eventID || !data.recipientDeviceID) {
      throw new HttpsError("invalid-argument", "eventID 和 recipientDeviceID 為必填欄位");
    }
    const acknowledgedAt = Date.now();
    await realtimeDb!.ref(`${realtimePaths.alertEvents}/${data.eventID}/recipients/${data.recipientDeviceID}`).update({ status: "acknowledged", acknowledgedTimestamp: acknowledgedAt, lastUpdateTimestamp: acknowledgedAt });
    const eventRef = firestore!.collection(firestoreCollections.alertEvents).doc(data.eventID);
    await firestore!.runTransaction(async (transaction) => {
      const eventDoc = await transaction.get(eventRef);
      if (eventDoc.exists) {
        transaction.update(eventRef, { acknowledgedCount: FieldValue.increment(1) });
      }
    });
    logger.info(`通知確認成功: ${data.eventID} by ${data.recipientDeviceID}`);
    return { success: true, message: "通知確認成功", acknowledgedAt };
  } catch (error) {
    logger.error("確認通知錯誤:", error);
    if (error instanceof HttpsError) throw error;
    throw new HttpsError("internal", "確認通知失敗");
  }
});

/**
 * 取消一個已發送的通知。
 * **注意**: 為了測試目的，暫時移除了身份驗證。
 */
export const cancelAlert = onCall(async (request: CallableRequest<CancelAlertRequest>) => {
  // if (!request.auth) {
  //   logger.error("cancelAlert 呼叫失敗: 未經授權的使用者。");
  //   throw new HttpsError("unauthenticated", "此操作需要使用者登入。");
  // }

  try {
    const { firestore, realtimeDb } = getFirebaseServices();
    const data = request.data;
    
    if (!data.eventID || !data.initiatorDeviceID) {
      throw new HttpsError("invalid-argument", "eventID 和 initiatorDeviceID 為必填欄位");
    }
    const cancelledAt = Date.now();
    const eventSnapshot = await realtimeDb!.ref(`${realtimePaths.alertEvents}/${data.eventID}`).once("value");
    if (!eventSnapshot.exists()) throw new HttpsError("not-found", "通知事件不存在");
    const eventData = eventSnapshot.val();
    if (eventData.initiatorDeviceID !== data.initiatorDeviceID) {
      throw new HttpsError("permission-denied", "只有發起者可以取消通知");
    }
    await realtimeDb!.ref(`${realtimePaths.alertEvents}/${data.eventID}/status`).set("cancelled");
    await firestore!.collection(firestoreCollections.alertEvents).doc(data.eventID).update({ status: "cancelled" });
    logger.info(`通知事件取消成功: ${data.eventID}`);
    return { success: true, message: "通知事件取消成功", cancelledAt };
  } catch (error) {
    logger.error("取消通知事件錯誤:", error);
    if (error instanceof HttpsError) throw error;
    throw new HttpsError("internal", "取消通知事件失敗");
  }
});

/**
 * 初始化員工數據。
 * **注意**: 為了測試目的，暫時移除了身份驗證。
 */
export const initializeStaffData = onCall(async (request: CallableRequest) => {
  // if (!request.auth) {
  //   logger.error("initializeStaffData 呼叫失敗: 未經授權的使用者。");
  //   throw new HttpsError("unauthenticated", "此操作需要管理者權限。");
  // }
  
  try {
    const { firestore } = getFirebaseServices();
    const staffData = [
      { id: "staff_1", name: "Jane Doe", role: "Obstetrician", initials: "JD", color: "#3B82F6", phoneNumber: "******-0101" },
      { id: "staff_2", name: "Sarah Miller", role: "Midwife", initials: "SM", color: "#8B5CF6", phoneNumber: "******-0102" },
    ];
    const batch = firestore!.batch();
    const now = FieldValue.serverTimestamp();
    staffData.forEach((staff) => {
      const userRef = firestore!.collection(firestoreCollections.users).doc(staff.id);
      batch.set(userRef, {
        deviceID: staff.id, nickname: staff.name, fcmToken: "", name: staff.name,
        role: staff.role, initials: staff.initials, color: staff.color, phoneNumber: staff.phoneNumber,
        avatar: "", createdAt: now, lastSeen: now, isActive: true,
      });
    });
    await batch.commit();
    logger.info(`員工數據初始化完成，共 ${staffData.length} 筆記錄`);
    return { success: true, message: `員工數據初始化完成，共 ${staffData.length} 筆記錄`, initializedCount: staffData.length };
  } catch (error) {
    logger.error("初始化員工數據錯誤:", error);
    throw new HttpsError("internal", "初始化員工數據失敗");
  }
});

/**
 * [背景觸發] 當有新的通知事件寫入 Firestore 時，更新 Realtime Database 中的統計數據。
 */
export const updateNotificationStats = onDocumentCreated(`${firestoreCollections.alertEvents}/{eventID}`, async (event) => {
  try {
    const { realtimeDb } = getFirebaseServices();
    const statsRef = realtimeDb!.ref(`${realtimePaths.stats}/notifications`);
    await statsRef.transaction((currentStats) => {
      const stats = currentStats || { totalAlerts: 0, activeAlerts: 0, todayAlerts: 0, lastUpdated: 0 };
      stats.totalAlerts += 1;
      stats.activeAlerts += 1;
      const today = new Date().toDateString();
      const lastUpdatedDate = new Date(stats.lastUpdated).toDateString();
      stats.todayAlerts = (today === lastUpdatedDate) ? stats.todayAlerts + 1 : 1;
      stats.lastUpdated = Date.now();
      return stats;
    });
    logger.info("通知統計更新成功");
  } catch (error) {
    logger.error("更新通知統計錯誤:", error);
  }
});

/**
 * [診斷用] 獲取並回傳當前的環境變數配置。
 */
export const diagnoseEnv = onCall((request: CallableRequest) => {
  logger.info("診斷環境變數請求被觸發");
  return {
    message: "成功讀取後端環境變數", timestamp: Date.now(), nodeEnv: process.env.NODE_ENV,
    gcloudProject: process.env.GCLOUD_PROJECT, isDevelopment: isDevelopment,
    firestoreUsersCollection: process.env.FIRESTORE_USERS_COLLECTION,
    firestoreEventsCollection: process.env.FIRESTORE_EVENTS_COLLECTION,
    firestoreGroupsCollection: process.env.FIRESTORE_GROUPS_COLLECTION,
    firestoreStaffCollection: process.env.FIRESTORE_STAFF_COLLECTION,
    realtimePresencePath: process.env.REALTIME_PRESENCE_PATH,
    realtimeEventsPath: process.env.REALTIME_EVENTS_PATH,
    realtimeStatsPath: process.env.REALTIME_STATS_PATH,
    actualCollections: firestoreCollections, actualPaths: realtimePaths,
    firebaseConfig: process.env.FIREBASE_CONFIG, port: process.env.PORT,
    functionsControlApi: process.env.FUNCTIONS_CONTROL_API
  };
});

/**
 * [診斷用] 簡單的健康檢查函式。
 */
export const healthCheck = onCall(async (request: CallableRequest) => {
  try {
    logger.info("健康檢查請求");
    return {
      success: true, status: "healthy", timestamp: Date.now(), message: "QMNotiAugment Cloud Functions 運行正常",
      environment: process.env.NODE_ENV, isDevelopment: isDevelopment,
      collections: { firestore: firestoreCollections, realtimeDB: realtimePaths },
      firebaseProject: process.env.GCLOUD_PROJECT
    };
  } catch (error) {
    logger.error("健康檢查錯誤:", error);
    throw new HttpsError("internal", "健康檢查失敗");
  }
});
