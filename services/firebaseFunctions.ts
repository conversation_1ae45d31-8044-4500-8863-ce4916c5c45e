/**
 * Firebase 統一服務
 * 整合 Firebase 客戶端服務、Cloud Functions 和工具函數
 * 提供 Firestore、Realtime Database 和 Cloud Functions 的統一接口
 */

// Firebase 核心導入
import { app, auth, firestoreDB as firestore, realtimeDB as realtimeDb, functions } from '../firebaseConfig';
import { httpsCallable } from 'firebase/functions';

// React Native 相關導入
import { Platform } from 'react-native';
import * as Device from 'expo-device';
import messaging from '@react-native-firebase/messaging';

// 環境變數導入
import { NODE_ENV } from '@env';

// 類型導入
import type {
  RegisterUserRequest,
  RegisterUserResponse,
  CreateAlertRequest,
  CreateAlertResponse,
  AcknowledgeAlertRequest,
  AcknowledgeAlertResponse,
  CancelAlertRequest,
  CancelAlertResponse,
} from '@/types/firebase';

// ==========================================
// 導出 Firebase 服務實例
// ==========================================

/**
 * 導出Firebase服務實例
 * 這些實例已經在firebaseConfig.ts中正確配置了環境切換邏輯
 */
export { app, auth, firestore, realtimeDb, functions };

// ==========================================
// 集合配置管理
// ==========================================

/**
 * 集合配置介面
 */
interface CollectionConfig {
  firestore: {
    users: string;
    alertEvents: string;
    groups: string;
    staff: string;
  };
  realtimeDB: {
    presence: string;
    alertEvents: string;
    stats: string;
  };
}

/**
 * 判斷是否為開發環境
 */
const isDevelopment = NODE_ENV === 'development';

/**
 * 從環境變數獲取集合配置
 * 所有環境都使用 .env 文件中定義的集合名稱
 * 環境隔離通過不同的 Firebase 項目實現
 */
const getCollectionsFromEnv = (): CollectionConfig => {
  // 在 React Native 環境中，使用默認值，因為 @env 變數在編譯時處理
  return {
    firestore: {
      users: 'users',
      alertEvents: 'alertEvents',
      groups: 'groups',
      staff: 'staff'
    },
    realtimeDB: {
      presence: 'presence',
      alertEvents: 'alertEvents',
      stats: 'stats'
    }
  };
};

/**
 * 獲取當前環境的集合配置
 */
export function getCollectionConfig(): CollectionConfig {
  const config = getCollectionsFromEnv();

  // 輸出當前環境信息
  if (isDevelopment) {
    console.log('🧪 開發環境：使用測試 Firebase 項目 (qmnoti-test)');
    console.log('📊 集合名稱 (來自環境變數):');
    Object.entries(config.firestore).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    console.log('📈 Realtime Database 路徑:');
    Object.entries(config.realtimeDB).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
  } else {
    console.log('🚀 生產環境：使用正式 Firebase 項目 (qmnoti)');
    console.log('📊 集合名稱 (來自環境變數):', Object.values(config.firestore).join(', '));
  }

  return config;
}

/**
 * 獲取 Firestore 集合名稱
 */
export function getFirestoreCollections() {
  return getCollectionConfig().firestore;
}

/**
 * 獲取 Realtime Database 路徑
 */
export function getRealtimeDatabasePaths() {
  return getCollectionConfig().realtimeDB;
}

// ==========================================
// 工具函數
// ==========================================

/**
 * 檢查是否為測試數據
 */
export function isTestData(data: any): boolean {
  if (typeof data === 'string') {
    return data.includes('test') || data.includes('Test') || data.includes('測試');
  }

  if (typeof data === 'object' && data !== null) {
    const deviceID = data.deviceID || data.id;
    if (deviceID && typeof deviceID === 'string') {
      return deviceID.includes('test') || deviceID.includes('Test');
    }

    const nickname = data.nickname || data.name;
    if (nickname && typeof nickname === 'string') {
      return nickname.includes('Test') || nickname.includes('測試') || nickname.includes('test');
    }
  }

  return false;
}

/**
 * 生成測試設備 ID
 */
export function generateTestDeviceID(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `test_device_${timestamp}_${random}`;
}

/**
 * 生成測試事件 ID
 */
export function generateTestEventID(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `test_event_${timestamp}_${random}`;
}

/**
 * 環境信息
 */
export function getEnvironmentInfo() {
  return {
    isDevelopment,
    nodeEnv: NODE_ENV,
    collections: getCollectionConfig(),
    firebaseProject: isDevelopment ? 'qmnoti-test' : 'qmnoti',
    isolationMethod: 'separate-firebase-projects'
  };
}

/**
 * 環境檢測工具函數
 */
export const debugEnvironment = () => {
  const envInfo = {
    nodeEnv: process.env.NODE_ENV,
    platform: Platform.OS,
    isDevice: Device.isDevice,
    deviceName: Device.deviceName,
    osName: Device.osName,
    osVersion: Device.osVersion,
    firebaseRegion: 'asia-east1',
    timestamp: new Date().toISOString()
  };
  
  console.log('🔍 環境檢測信息:', JSON.stringify(envInfo, null, 2));
  
  // 檢查Firebase Functions是否正確配置
  if (functions) {
    console.log('✅ Firebase Functions實例已配置');
    console.log(`📍 Functions區域: ${functions.region || 'default'}`);
  } else {
    console.error('❌ Firebase Functions實例未配置');
  }
  
  return envInfo;
};

// ==========================================
// Cloud Functions 客戶端接口
// ==========================================

/**
 * 用戶註冊/更新 Cloud Function
 */
export const registerUserFunction = httpsCallable<RegisterUserRequest, RegisterUserResponse>(
  functions,
  'registerUser'
);

/**
 * 創建通知事件 Cloud Function
 */
export const createAlertFunction = httpsCallable<CreateAlertRequest, CreateAlertResponse>(
  functions,
  'createAlert'
);

/**
 * 確認通知 Cloud Function
 */
export const acknowledgeAlertFunction = httpsCallable<AcknowledgeAlertRequest, AcknowledgeAlertResponse>(
  functions,
  'acknowledgeAlert'
);

/**
 * 取消通知 Cloud Function
 */
export const cancelAlertFunction = httpsCallable<CancelAlertRequest, CancelAlertResponse>(
  functions,
  'cancelAlert'
);

/**
 * 初始化員工數據 Cloud Function
 */
export const initializeStaffDataFunction = httpsCallable(functions, 'initializeStaffData');

/**
 * 健康檢查 Cloud Function
 */
export const healthCheckFunction = httpsCallable(functions, 'healthCheck');

// 工具函數：獲取設備唯一 ID
export const getDeviceID = async (): Promise<string> => {
  try {
    // 優先使用設備的唯一識別符
    if (Device.deviceName && Device.osName && Device.osVersion) {
      return `${Device.osName}_${Device.deviceName}_${Device.osVersion}`.replace(/[^a-zA-Z0-9_-]/g, '_');
    }
    
    // 如果無法獲取設備信息，使用當前時間戳和隨機數生成
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `device_${timestamp}_${random}`;
  } catch (error) {
    console.error('生成設備 ID 時發生錯誤:', error);
    // 備用方案：使用時間戳和隨機數
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `device_${timestamp}_${random}`;
  }
};

/**
 * 獲取設備的原生 FCM Token。
 * 此函數專為 Bare Workflow 設計，並直接依賴 @react-native-firebase/messaging。
 * 它移除了對 expo-notifications 的錯誤回退邏輯。
 */
export const getFCMToken = async (): Promise<string> => {
  console.log('開始獲取原生 FCM Token...');

  // 檢查是否為模擬器環境
  const isEmulatorEnv = process.env.NODE_ENV === 'development';
  
  // 1. 檢查是否在真實設備上
  // 在模擬器/模擬器上，無法獲取真實 FCM Token
  if (!Device.isDevice) {
    const simulatorToken = `simulator_fcm_token_${Date.now()}`;
    console.warn(`正在模擬器上運行，將返回一個模擬的 FCM Token: ${simulatorToken}`);
    return simulatorToken;
  }

  // 2. 真實設備在模擬器環境下的特殊處理
  if (isEmulatorEnv && Device.isDevice) {
    console.log('🔧 檢測到真實設備在模擬器環境下運行');
    
    try {
      // 首先嘗試獲取真實的FCM Token
      const authStatus = await messaging().requestPermission();
      const hasPermission =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (hasPermission) {
        console.log('✅ 推播通知權限已獲得，嘗試獲取真實FCM Token...');
        const realToken = await messaging().getToken();
        
        if (realToken && realToken.length > 20) {
          console.log('✅ 成功獲取真實FCM Token (模擬器環境):', realToken.substring(0, 20) + '...');
          return realToken;
        }
      }
    } catch (error) {
      console.warn('⚠️ 在模擬器環境下無法獲取真實FCM Token:', error);
    }
    
    // 如果無法獲取真實FCM Token，生成一個測試用的Token
    const testToken = `test_fcm_token_${Device.osName}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    console.log(`🧪 模擬器環境：為真實設備生成測試FCM Token: ${testToken}`);
    return testToken;
  }
  
  // 3. 真實設備在生產環境下的標準處理
  console.log('🚀 生產環境：正常獲取FCM Token...');
  
  // 請求推播通知權限
  const authStatus = await messaging().requestPermission();
  const hasPermission =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (!hasPermission) {
    const noPermissionToken = `no_permission_token_${Date.now()}`;
    console.warn(`用戶拒絕了推播通知權限！返回一個無權限的標識 Token: ${noPermissionToken}`);
    return noPermissionToken;
  }
  
  console.log('推播通知權限已獲得。');

  // 獲取原生的 FCM Token
  try {
    console.log('嘗試透過 @react-native-firebase/messaging 獲取 Token...');
    const fcmToken = await messaging().getToken();
    
    if (fcmToken) {
        console.log('✅ 成功獲取原生 FCM Token:', fcmToken);
        return fcmToken;
    } else {
        console.error('❌ messaging().getToken() 返回了空值！');
        throw new Error('無法獲取 FCM Token，Firebase 返回為空。');
    }

  } catch (error) {
    console.error('❌ 獲取原生 FCM Token 失敗! 這通常表示原生設定有誤。', error);
    throw new Error('無法獲取 FCM Token。請檢查 app.config.js 的 plugins 並執行 npx expo prebuild --clean。');
  }
};

// ==========================================
// Firebase 服務包裝器類
// ==========================================

/**
 * Firebase 服務包裝器類
 * 提供統一的錯誤處理和類型安全的接口
 */
export class FirebaseService {
  /**
   * 用戶註冊
   */
  static async registerUser(userData: RegisterUserRequest): Promise<RegisterUserResponse> {
    try {
      const result = await registerUserFunction(userData);
      return result.data;
    } catch (error: any) {
      console.error('用戶註冊錯誤:', error);
      throw new Error(error.message || '用戶註冊失敗');
    }
  }

  /**
   * 創建通知事件
   */
  static async createAlert(alertData: CreateAlertRequest): Promise<CreateAlertResponse> {
    try {
      const result = await createAlertFunction(alertData);
      return result.data;
    } catch (error: any) {
      console.error('創建通知事件錯誤:', error);
      throw new Error(error.message || '創建通知事件失敗');
    }
  }

  /**
   * 確認通知
   */
  static async acknowledgeAlert(acknowledgeData: AcknowledgeAlertRequest): Promise<AcknowledgeAlertResponse> {
    try {
      const result = await acknowledgeAlertFunction(acknowledgeData);
      return result.data;
    } catch (error: any) {
      console.error('確認通知錯誤:', error);
      throw new Error(error.message || '確認通知失敗');
    }
  }

  /**
   * 取消通知
   */
  static async cancelAlert(cancelData: CancelAlertRequest): Promise<CancelAlertResponse> {
    try {
      const result = await cancelAlertFunction(cancelData);
      return result.data;
    } catch (error: any) {
      console.error('取消通知錯誤:', error);
      throw new Error(error.message || '取消通知失敗');
    }
  }

  /**
   * 初始化員工數據
   */
  static async initializeStaffData(): Promise<any> {
    try {
      const result = await initializeStaffDataFunction();
      return result.data;
    } catch (error: any) {
      console.error('初始化員工數據錯誤:', error);
      throw new Error(error.message || '初始化員工數據失敗');
    }
  }

  /**
   * 健康檢查
   */
  static async healthCheck(): Promise<any> {
    try {
      const result = await healthCheckFunction();
      return result.data;
    } catch (error: any) {
      console.error('健康檢查錯誤:', error);
      throw new Error(error.message || '健康檢查失敗');
    }
  }
}

/**
 * Firebase 連接狀態檢查
 */
export const checkFirebaseConnection = async (): Promise<boolean> => {
  try {
    await FirebaseService.healthCheck();
    return true;
  } catch (error) {
    console.error('Firebase 連接檢查失敗:', error);
    return false;
  }
};

// ==========================================
// 設備和通知相關函數
// ==========================================

/**
 * 註冊用戶 Cloud Function
 */
export const registerUser = async (userData: Omit<RegisterUserRequest, 'deviceID' | 'fcmToken'>): Promise<RegisterUserResponse> => {
  try {
    // 調試環境信息
    const envInfo = debugEnvironment();
    console.log('🔧 開始註冊用戶流程...');
    
    // 獲取設備 ID 和 FCM Token
    console.log('📱 獲取設備 ID...');
    const deviceID = await getDeviceID();
    console.log(`✅ 設備 ID: ${deviceID}`);
    
    console.log('🔔 獲取 FCM Token...');
    const fcmToken = await getFCMToken();
    console.log(`✅ FCM Token (前20字符): ${fcmToken.substring(0, 20)}...`);

    const requestData = {
      deviceID,
      fcmToken,
      ...userData,
    };
    
    console.log('📤 準備調用 registerUser Cloud Function...');
    console.log('📋 請求數據 (隱藏Token):', {
      ...requestData,
      fcmToken: `${fcmToken.substring(0, 10)}...(隱藏)`
    });

    const result = await registerUserFunction(requestData);

    console.log('✅ registerUser Cloud Function 調用成功');
    console.log('📥 響應數據:', result.data);

    return result.data;
  } catch (error) {
    console.error('❌ 註冊用戶時發生錯誤:', error);
    
    // 提供更詳細的錯誤信息
    if (error && typeof error === 'object') {
      const errorObj = error as any;
      if (errorObj.code) {
        console.error(`🔴 Firebase錯誤代碼: ${errorObj.code}`);
      }
      if (errorObj.message) {
        console.error(`🔴 錯誤訊息: ${errorObj.message}`);
      }
      if (errorObj.details) {
        console.error(`🔴 錯誤詳情:`, errorObj.details);
      }
    }
    
    throw new Error(`註冊失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 創建通知事件 Cloud Function
 */
export const createAlert = async (alertData: Omit<CreateAlertRequest, 'initiatorDeviceID'>): Promise<CreateAlertResponse> => {
  try {
    // 獲取發起者的設備 ID
    const initiatorDeviceID = await getDeviceID();

    const result = await createAlertFunction({
      initiatorDeviceID,
      ...alertData,
    });

    return result.data;
  } catch (error) {
    console.error('創建通知事件時發生錯誤:', error);
    throw new Error(`發送通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 確認通知 Cloud Function
 */
export const acknowledgeAlert = async (eventID: string): Promise<any> => {
  try {
    const recipientDeviceID = await getDeviceID();

    const result = await acknowledgeAlertFunction({
      eventID,
      recipientDeviceID,
    });

    return result.data;
  } catch (error) {
    console.error('確認通知時發生錯誤:', error);
    throw new Error(`確認通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

/**
 * 取消通知 Cloud Function
 */
export const cancelAlert = async (eventID: string): Promise<any> => {
  try {
    const initiatorDeviceID = await getDeviceID();

    const result = await cancelAlertFunction({
      eventID,
      initiatorDeviceID,
    });

    return result.data;
  } catch (error) {
    console.error('取消通知時發生錯誤:', error);
    throw new Error(`取消通知失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
};

// ==========================================
// 默認導出
// ==========================================

/**
 * 導出所有服務實例以便其他模組使用
 */
export default {
  // Firebase 實例
  app,
  firestore,
  realtimeDb,
  functions,
  auth,

  // 服務類
  FirebaseService,

  // 工具函數
  checkFirebaseConnection,
  getCollectionConfig,
  getFirestoreCollections,
  getRealtimeDatabasePaths,
  getEnvironmentInfo,
  isTestData,
  generateTestDeviceID,
  generateTestEventID,

  // 設備相關函數
  getDeviceID,
  getFCMToken,
  debugEnvironment,

  // Cloud Functions
  registerUser,
  createAlert,
  acknowledgeAlert,
  cancelAlert,
};