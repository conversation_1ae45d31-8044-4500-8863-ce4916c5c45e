import { StaffInfo, StaffGroup } from '@/types/staff';

/**
 * 統一的員工數據
 * 包含所有基礎信息：name, role, initials, color, phoneNumber
 */
export const STAFF_DATA: StaffInfo[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Obstetrician',
    initials: 'J<PERSON>',
    color: '#3B82F6',
    phoneNumber: '******-0101',
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Midwife',
    initials: '<PERSON>',
    color: '#8B5CF6',
    phoneNumber: '******-0102',
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'Anaesthetist',
    initials: 'PA',
    color: '#10B981',
    phoneNumber: '******-0103',
  },
  {
    id: '4',
    name: '<PERSON>',
    role: 'Nurse',
    initials: '<PERSON>',
    color: '#F59E0B',
    phoneNumber: '******-0104',
  },
  {
    id: '5',
    name: '<PERSON>',
    role: 'Pediatrician',
    initials: 'RB',
    color: '#EF4444',
    phoneNumber: '******-0105',
  },
  {
    id: '6',
    name: '<PERSON>',
    role: 'Surgeon',
    initials: 'ED',
    color: '#06B6D4',
    phoneNumber: '******-0106',
  },
  {
    id: '7',
    name: 'Carol Smith',
    role: 'Nurse',
    initials: 'CS',
    color: '#F97316',
    phoneNumber: '******-0107',
  },
  {
    id: '8',
    name: '<PERSON> <PERSON>',
    role: 'Technician',
    initials: 'MW',
    color: '#EC4899',
    phoneNumber: '******-0108',
  },
  {
    id: '9',
    name: 'Dr. Grace Hall',
    role: 'Chief Physician',
    initials: 'GH',
    color: '#8B5CF6',
    phoneNumber: '******-0109',
    avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBnewFcf7Z1ViEI-DOVLtEqAJfV2pZs34Get9JUj6algN83TwvSjmOdsJbz9tnPCm5B8tBgaJtTL5fjw-j_n-j8SeKCqIVnAhqZSnq1Wrtd59C9ESprBbkZ3Ks34Pew-YLbnzQo_EydE_Da8Yo6VzWut-2LaaJ2SUhFB-nMC6f9i1I16JJtLm1noYbh6NUSgIDGHo5kzd5EAg1cBgy_kW7sxayCrL-0BDfA8tSuMgU3u4uMgm8ifrTwtwlpkCXmWSg4gic4mhZpwvE',
  },
  {
    id: '10',
    name: 'Betty Early',
    role: 'Senior Nurse',
    initials: 'BE',
    color: '#10B981',
    phoneNumber: '******-0110',
    avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBChJV-xTPWOvyUt46X6mpENnob0sPNvXtcbXkfHD2NAEvwZ7xeBM7L0jskltmKPbnND_8ELJLcfZ2I4r4vMGbZgIJgtwtkfzuNUT5nDhBO4aHEzA8_YOkHj31VJMeJAPrNUYKLQ9r0q-JR_8McH6-Ko_4M_-wXjHY6WWufNmiain7SDFoFKhOMClkhero7EbeZQm9SoRtNb4RnV8Vm1ea2dqfOx6FrNBOxBeDBv9AsDF42lQ6XvElpVSQpkO6f0s7mwLSqd6XIBag',
  },
];

/**
 * 統一的員工分組數據 - 使用數字ID以便順序增加
 */
export let STAFF_GROUPS: StaffGroup[] = [
  {
    id: '1',
    name: 'Management Team',
    icon: 'business',
    color: '#8B5CF6',
    initials: 'MT',
    memberIds: ['9'], // Dr. Grace Hall
  },
  {
    id: '2',
    name: 'All Employees',
    icon: 'group',
    color: '#3B82F6',
    initials: 'AE',
    memberIds: ['4', '5', '6'], // Alice Johnson, Robert Brown, Emily Davis
  },
  {
    id: '3',
    name: 'Project Alpha Team',
    icon: 'group',
    color: '#10B981',
    initials: 'PA',
    memberIds: ['1', '2', '3'], // Jane Doe, Sarah Miller, Paul Allen
  },
  {
    id: '4',
    name: 'Nursing Staff',
    icon: 'local-hospital',
    color: '#F59E0B',
    initials: 'NS',
    memberIds: ['4', '7', '10'], // Alice Johnson, Carol Smith, Betty Early
  },
];

/**
 * 可用的群組圖標列表
 */
const AVAILABLE_GROUP_ICONS = [
  'group', 'business', 'local-hospital', 'engineering', 'admin-panel-settings',
  'work', 'assignment', 'group-work', 'supervisor-account', 'people',
  'medical-services', 'construction', 'psychology', 'science', 'school'
];

/**
 * 可用的群組顏色列表
 */
const AVAILABLE_GROUP_COLORS = [
  '#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444',
  '#06B6D4', '#F97316', '#EC4899', '#84CC16', '#6366F1'
];

/**
 * 生成新的群組ID
 */
export function generateNewGroupId(): string {
  const existingIds = STAFF_GROUPS.map(group => parseInt(group.id));
  const maxId = Math.max(...existingIds);
  return (maxId + 1).toString();
}

/**
 * 生成隨機圖標
 */
export function getRandomGroupIcon(): string {
  const randomIndex = Math.floor(Math.random() * AVAILABLE_GROUP_ICONS.length);
  return AVAILABLE_GROUP_ICONS[randomIndex];
}

/**
 * 生成隨機顏色
 */
export function getRandomGroupColor(): string {
  const randomIndex = Math.floor(Math.random() * AVAILABLE_GROUP_COLORS.length);
  return AVAILABLE_GROUP_COLORS[randomIndex];
}

/**
 * 生成群組首字母縮寫
 */
export function generateGroupInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 2);
}

/**
 * 添加新群組
 */
export function addGroup(groupData: { name: string; memberIds: string[] }): StaffGroup {
  const newGroup: StaffGroup = {
    id: generateNewGroupId(),
    name: groupData.name,
    icon: getRandomGroupIcon(),
    color: getRandomGroupColor(),
    initials: generateGroupInitials(groupData.name),
    memberIds: groupData.memberIds,
  };
  
  STAFF_GROUPS.push(newGroup);
  return newGroup;
}

/**
 * 更新群組
 */
export function updateGroup(groupId: string, groupData: { name: string; memberIds: string[] }): boolean {
  const groupIndex = STAFF_GROUPS.findIndex(group => group.id === groupId);
  if (groupIndex === -1) return false;
  
  STAFF_GROUPS[groupIndex] = {
    ...STAFF_GROUPS[groupIndex],
    name: groupData.name,
    initials: generateGroupInitials(groupData.name),
    memberIds: groupData.memberIds,
  };
  
  return true;
}

/**
 * 刪除群組
 */
export function deleteGroup(groupId: string): boolean {
  const groupIndex = STAFF_GROUPS.findIndex(group => group.id === groupId);
  if (groupIndex === -1) return false;
  
  STAFF_GROUPS.splice(groupIndex, 1);
  return true;
}

/**
 * 根據ID獲取員工信息
 */
export function getStaffById(staffId: string): StaffInfo | undefined {
  return STAFF_DATA.find(staff => staff.id === staffId);
}

/**
 * 根據ID獲取分組信息
 */
export function getGroupById(groupId: string): StaffGroup | undefined {
  return STAFF_GROUPS.find(group => group.id === groupId);
}

/**
 * 根據分組ID獲取分組成員信息
 */
export function getGroupMembers(groupId: string): StaffInfo[] {
  const group = getGroupById(groupId);
  if (!group || !group.memberIds) return [];
  
  return group.memberIds
    .map(id => getStaffById(id))
    .filter((staff): staff is StaffInfo => staff !== undefined);
}

/**
 * 根據職位篩選員工
 */
export function getStaffByRole(role: string): StaffInfo[] {
  return STAFF_DATA.filter(staff => staff.role.toLowerCase().includes(role.toLowerCase()));
}

/**
 * 搜尋員工（根據姓名或職位）
 */
export function searchStaff(query: string): StaffInfo[] {
  const lowercaseQuery = query.toLowerCase();
  return STAFF_DATA.filter(staff => 
    staff.name.toLowerCase().includes(lowercaseQuery) ||
    staff.role.toLowerCase().includes(lowercaseQuery)
  );
}

/**
 * 搜尋分組（根據名稱）
 */
export function searchGroups(query: string): StaffGroup[] {
  const lowercaseQuery = query.toLowerCase();
  return STAFF_GROUPS.filter(group => 
    group.name.toLowerCase().includes(lowercaseQuery)
  );
} 