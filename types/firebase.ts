import { Timestamp } from 'firebase/firestore';

/**
 * 母嬰轉送個案類型
 */
export type CaseType = 
  | 'mother_baby_transfer'    // 母親與嬰兒轉送
  | 'mother_only_transfer'    // 僅母親轉送
  | 'baby_to_nicu';          // 嬰兒轉送至 NICU/SCBU

/**
 * 通知接收者狀態
 */
export type RecipientStatus = 
  | 'send_failed'             // 發送失敗 (紅色)
  | 'fcm_sent_pending_ack'    // 已發送，等待確認 (黃色)
  | 'acknowledged';           // 已確認 (綠色)

/**
 * 通知事件狀態
 */
export type AlertEventStatus = 
  | 'active'                  // 活動中
  | 'resolved'                // 已解決
  | 'cancelled';              // 已取消

/**
 * Firebase 用戶資料 (Firestore)
 * 擴展自現有的 StaffInfo，添加 Firebase 特定字段
 */
export interface FirebaseUser {
  deviceID: string;           // 設備唯一標識符 (document ID)
  nickname: string;           // 用戶設定的暱稱
  fcmToken: string;          // Firebase Cloud Messaging Token
  name?: string;             // 真實姓名 (來自 staff.ts)
  role?: string;             // 職位角色 (來自 staff.ts)
  initials?: string;         // 姓名縮寫
  color?: string;            // 顯示顏色
  phoneNumber?: string;      // 電話號碼
  avatar?: string;           // 頭像 URL
  createdAt: Timestamp;      // 創建時間
  lastSeen: Timestamp;       // 最後活動時間
  isActive: boolean;         // 是否啟用
}

/**
 * Firebase 員工群組 (Firestore)
 * 擴展自現有的 StaffGroup，添加 Firebase 特定字段
 */
export interface FirebaseStaffGroup {
  id: string;                // 群組 ID (document ID)
  name: string;              // 群組名稱
  icon: string;              // Material Icons 名稱
  color: string;             // 顯示顏色
  initials: string;          // 群組縮寫
  memberIds: string[];       // 成員 deviceID 列表
  createdBy: string;         // 創建者 deviceID
  createdAt: Timestamp;      // 創建時間
  updatedAt: Timestamp;      // 最後更新時間
  isActive: boolean;         // 是否啟用
}

/**
 * 通知事件基本信息 (Firestore)
 */
export interface AlertEventFirestore {
  eventID: string;           // 事件 ID (document ID)
  initiatorDeviceID: string; // 發起者設備 ID
  initiatorNickname: string; // 發起者暱稱
  caseType: CaseType;        // 個案類型
  motherInitial: string;     // 母親姓名縮寫
  bedNumber?: string;        // 床號 (可選)
  designatedWard: string;    // 目標病房
  clinicalNotes?: string;    // 臨床記錄 (可選)
  createdAt: Timestamp;      // 創建時間
  status: AlertEventStatus;  // 事件狀態
  totalRecipients: number;   // 總接收人數
  acknowledgedCount: number; // 已確認人數
}

/**
 * 通知接收者信息 (Realtime Database)
 */
export interface NotificationRecipient {
  nickname: string;          // 接收者暱稱
  status: RecipientStatus;   // 接收狀態
  fcmTokenUsed?: string;     // 使用的 FCM Token (用於調試)
  lastUpdateTimestamp: number; // 狀態更新時間 (Unix timestamp)
  acknowledgedTimestamp?: number; // 確認時間 (如果已確認)
  errorMessage?: string;     // 錯誤信息 (如果發送失敗)
}

/**
 * 通知事件實時狀態 (Realtime Database)
 */
export interface AlertEventRealtime {
  // 基本事件信息 (映射自 Firestore)
  initiatorDeviceID: string;
  initiatorNickname: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  timestampCreated: number;  // Unix timestamp
  status: AlertEventStatus;
  
  // 實時接收者狀態
  recipients: {
    [deviceID: string]: NotificationRecipient;
  };
}

/**
 * 用戶在線狀態 (Realtime Database)
 */
export interface UserPresence {
  isOnline: boolean;
  lastSeen: number;          // Unix timestamp
  fcmTokenActive: boolean;   // FCM Token 是否有效
}

/**
 * 實時統計 (Realtime Database)
 */
export interface NotificationStats {
  totalAlerts: number;
  activeAlerts: number;
  todayAlerts: number;
  lastUpdated: number;       // Unix timestamp
}

/**
 * Cloud Function 請求/響應類型
 */

// registerUser 請求
export interface RegisterUserRequest {
  deviceID: string;
  nickname: string;
  fcmToken: string;
  name?: string;
  role?: string;
  initials?: string;
  color?: string;
  phoneNumber?: string;
  avatar?: string;
}

// registerUser 響應
export interface RegisterUserResponse {
  success: boolean;
  message: string;
  user?: FirebaseUser;
}

// createAlert 請求
export interface CreateAlertRequest {
  initiatorDeviceID: string;
  caseType: CaseType;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  recipientDeviceIDs: string[];
}

// createAlert 響應
export interface CreateAlertResponse {
  success: boolean;
  message: string;
  eventID?: string;
  failedRecipients?: {
    deviceID: string;
    reason: string;
  }[];
}

// acknowledgeAlert 請求
export interface AcknowledgeAlertRequest {
  eventID: string;
  recipientDeviceID: string;
}

// acknowledgeAlert 響應
export interface AcknowledgeAlertResponse {
  success: boolean;
  message: string;
  acknowledgedAt?: number; // Unix timestamp
}

// cancelAlert 請求
export interface CancelAlertRequest {
  eventID: string;
  initiatorDeviceID: string;
}

// cancelAlert 響應
export interface CancelAlertResponse {
  success: boolean;
  message: string;
  cancelledAt?: number; // Unix timestamp
}

/**
 * FCM 推播通知 Payload
 */
export interface FCMNotificationPayload {
  notification: {
    title: string;
    body: string;
  };
  data: {
    eventID: string;
    caseType: string;
    motherInitial: string;
    bedNumber?: string;
    designatedWard: string;
    clinicalNotes?: string;
    initiatorNickname: string;
  };
  android: {
    priority: 'high';
    notification: {
      channelId: 'maternal_alerts';
      priority: 'high';
      sound: 'default';
      vibrationPattern: number[];
    };
  };
  apns: {
    headers: {
      'apns-priority': '10';
    };
    payload: {
      aps: {
        alert: {
          title: string;
          body: string;
        };
        sound: 'default';
        badge: number;
      };
    };
  };
}

/**
 * 錯誤類型定義
 */
export interface FirebaseError {
  code: string;
  message: string;
  details?: any;
}

/**
 * 資料驗證結果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * 批次操作結果
 */
export interface BatchOperationResult {
  success: boolean;
  successCount: number;
  failureCount: number;
  errors: {
    id: string;
    error: string;
  }[];
} 