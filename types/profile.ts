/**
 * Profile-related TypeScript type definitions
 */

/**
 * User role enumeration
 */
export type UserRole = 'developer' | 'designer' | 'manager' | 'nurse' | 'doctor' | 'other';

/**
 * User profile interface
 */
export interface UserProfile {
  /** User's full name */
  name: string;
  
  /** Name initials (for avatar display) */
  initials: string;
  
  /** User's role/position */
  role: UserRole;
  
  /** Hong Kong phone number (optional) */
  phoneNumber?: string;
  
  /** Profile icon color */
  color: string;
  
  /** Profile creation timestamp */
  createdAt?: number;
  
  /** Profile last update timestamp */
  updatedAt?: number;
}

/**
 * Default color options
 */
export const PROFILE_COLORS = [
  '#FF6347', // Tomato
  '#4682B4', // SteelBlue
  '#32CD32', // LimeGreen
  '#FFD700', // Gold
  '#DA70D6', // Orchid
  '#87CEFA', // SkyBlue
] as const;

/**
 * Role option configuration
 */
export const ROLE_OPTIONS: Array<{ value: UserRole; label: string }> = [
  { value: 'developer', label: 'Developer' },
  { value: 'designer', label: 'Designer' },
  { value: 'manager', label: 'Manager' },
  { value: 'nurse', label: 'Nurse' },
  { value: 'doctor', label: 'Doctor' },
  { value: 'other', label: 'Other' },
] as const; 