/**
 * Staff 相關的 TypeScript 類型定義
 */

/**
 * 員工基礎信息接口
 */
export interface StaffInfo {
  /** 員工唯一標識符 */
  id: string;
  
  /** 員工姓名 */
  name: string;
  
  /** 員工職位 */
  role: string;
  
  /** 姓名首字母縮寫 */
  initials: string;
  
  /** 顯示顏色 */
  color: string;
  
  /** 員工頭像URL（可選） */
  avatar?: string;
  
  /** 電話號碼 */
  phoneNumber: string;
}

/**
 * 通知接收者狀態枚舉
 */
export type RecipientStatus = 'pending' | 'received' | 'confirmed';

/**
 * 通知接收者信息接口（擴展自基礎員工信息）
 */
export interface NotificationRecipient extends StaffInfo {
  /** 接收狀態 */
  status: RecipientStatus;
  
  /** 是否為通知發起人 */
  isInitiator?: boolean;
  
  /** 是否為當前用戶 */
  isCurrentUser?: boolean;
  
  /** 最後更新時間 */
  lastUpdated?: Date;
}

/**
 * 員工分組信息接口
 */
export interface StaffGroup {
  /** 分組唯一標識符 */
  id: string;
  
  /** 分組名稱 */
  name: string;
  
  /** 分組圖標 (Material Icons name) */
  icon: any; // 使用 any 來支持 MaterialIcons 的所有圖標名稱
  
  /** 分組顏色 */
  color: string;
  
  /** 分組首字母縮寫 */
  initials: string;
  
  /** 分組成員ID列表（可選，用於動態分組） */
  memberIds?: string[];
}

/**
 * 選擇項目接口（用於統一處理員工和分組的選擇）
 */
export interface SelectableItem {
  id: string;
  name: string;
  initials: string;
  color: string;
  type: 'staff' | 'group';
} 