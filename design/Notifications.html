<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Notifications</title>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script>
<style>
        body {
            font-family: 'Roboto', sans-serif;
        }
        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 24px;display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-100">
<div class="max-w-sm mx-auto bg-white h-screen flex flex-col">
<header class="bg-slate-700 text-white p-4 flex items-center sticky top-0 z-10">
<button class="material-icons text-white mr-4">arrow_back_ios</button>
<h1 class="text-xl font-semibold">Notifications</h1>
</header>
<main class="flex-grow overflow-y-auto">
<section class="bg-white py-3">
<h2 class="text-sm font-semibold text-slate-700 px-4 pb-2">Pending Notifications</h2>
<div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-0.5">
<div class="flex items-center justify-between">
<div>
<h3 class="font-semibold text-slate-800">Mother/Baby Transfer</h3>
<p class="text-sm text-slate-600">Mother: Jessica Davis, Baby: Girl</p>
<p class="text-xs text-slate-500">8:45 AM</p>
</div>
<div class="flex items-center">
<span class="bg-yellow-100 text-yellow-600 text-xs font-semibold px-2 py-1 rounded-full flex items-center">
<span class="material-icons text-sm mr-1">hourglass_empty</span>
                                Pending
                            </span>
<span class="material-icons text-slate-400 ml-2">chevron_right</span>
</div>
</div>
</div>
<div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
<div class="flex items-center justify-between">
<div>
<h3 class="font-semibold text-slate-800">Mother/Baby Transfer</h3>
<p class="text-sm text-slate-600">Mother: Sophia Brown, Baby: Girl</p>
<p class="text-xs text-slate-500">Yesterday, 9:03 PM</p>
</div>
<div class="flex items-center">
<span class="bg-yellow-100 text-yellow-600 text-xs font-semibold px-2 py-1 rounded-full flex items-center">
<span class="material-icons text-sm mr-1">hourglass_empty</span>
                                Pending
                            </span>
<span class="material-icons text-slate-400 ml-2">chevron_right</span>
</div>
</div>
</div>
</section>
<section class="bg-white py-3 mt-2">
<div class="flex items-center justify-between px-4 pb-2">
<h2 class="text-sm font-semibold text-slate-700">Confirmed Notifications</h2>
<span class="material-icons text-slate-400 transform rotate-90">chevron_right</span>
</div>
<div class="bg-green-50 border-l-4 border-green-500 p-4 mb-0.5">
<div class="flex items-center justify-between">
<div>
<h3 class="font-semibold text-slate-800">Medication Administered</h3>
<p class="text-sm text-slate-600">Patient: John Doe - Pain Reliever</p>
<p class="text-xs text-slate-500">Today, 10:15 AM</p>
</div>
<div class="flex items-center">
<span class="bg-green-100 text-green-600 text-xs font-semibold px-2 py-1 rounded-full flex items-center">
<span class="material-icons text-sm mr-1">check_circle_outline</span>
                                Confirmed
                            </span>
<span class="material-icons text-slate-400 ml-2">chevron_right</span>
</div>
</div>
</div>
<div class="bg-green-50 border-l-4 border-green-500 p-4 mb-0.5">
<div class="flex items-center justify-between">
<div>
<h3 class="font-semibold text-slate-800">Lab Results Available</h3>
<p class="text-sm text-slate-600">Patient: Jane Smith - Blood Test</p>
<p class="text-xs text-slate-500">Yesterday, 5:30 PM</p>
</div>
<div class="flex items-center">
<span class="bg-green-100 text-green-600 text-xs font-semibold px-2 py-1 rounded-full flex items-center">
<span class="material-icons text-sm mr-1">check_circle_outline</span>
                                Confirmed
                            </span>
<span class="material-icons text-slate-400 ml-2">chevron_right</span>
</div>
</div>
</div>
<div class="bg-green-50 border-l-4 border-green-500 p-4">
<div class="flex items-center justify-between">
<div>
<h3 class="font-semibold text-slate-800">Discharge Approved</h3>
<p class="text-sm text-slate-600">Patient: Michael Johnson</p>
<p class="text-xs text-slate-500">2 days ago, 11:00 AM</p>
</div>
<div class="flex items-center">
<span class="bg-green-100 text-green-600 text-xs font-semibold px-2 py-1 rounded-full flex items-center">
<span class="material-icons text-sm mr-1">check_circle_outline</span>
                                Confirmed
                            </span>
<span class="material-icons text-slate-400 ml-2">chevron_right</span>
</div>
</div>
</div>
<p class="text-xs text-slate-500 px-4 py-3 text-center">Notifications older than 24 hours are automatically cleared.</p>
</section>
</main>
<nav class="bg-white border-t border-slate-200 sticky bottom-0">
<div class="flex justify-around items-center h-16">
<!-- <a class="flex flex-col items-center text-slate-500 hover:text-blue-500" href="#">
<span class="material-icons">home</span>
<span class="text-xs">Home</span>
</a> -->
<a class="flex flex-col items-center text-blue-500 relative" href="#">
<span class="material-icons">notifications</span>
<span class="text-xs font-semibold">Notifications</span>
<span class="absolute top-0 right-0 -mt-1 -mr-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">2</span>
</a>
<!-- <a class="flex flex-col items-center text-slate-500 hover:text-blue-500" href="#">
<span class="material-icons">person</span>
<span class="text-xs">Profile</span>
</a> -->
</div>
</nav>
</div>

</body></html>