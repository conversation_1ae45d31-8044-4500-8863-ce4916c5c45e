<html><head>
    <meta charset="utf-8"/>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet"/>
    <title>Stitch Design</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        body {
          min-height: max(884px, 100dvh);
        }
      </style>
      </head>
    <body class="bg-white">
    <div class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
    <div class="flex flex-col">
    <header class="sticky top-0 z-10 bg-white shadow-sm">
    <div class="mx-auto flex max-w-md items-center justify-between px-4 py-3">
    <button class="text-slate-700 hover:text-slate-900" type="button">
    <span class="material-symbols-outlined">close</span>
    </button>
    <h1 class="text-xl font-semibold text-slate-900">Edit Profile</h1>
    <div class="w-8"></div> 
    </div>
    </header>
    <main class="flex-1 overflow-y-auto bg-slate-50 pb-6 pt-2">
    <div class="mx-auto max-w-md space-y-6 px-4">
    <div class="flex justify-center py-6">
    <div class="flex size-32 items-center justify-center rounded-full bg-slate-200 text-5xl font-bold text-slate-600 shadow-md" id="user-icon-preview" style="background-color: #4682B4; color: white;">JD</div>
    </div>
    <div>
    <label class="mb-1.5 block text-sm font-medium text-slate-700" for="name">Name</label>
    <input class="form-input block w-full rounded-xl border-slate-300 bg-white p-3.5 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="name" placeholder="Enter your full name" type="text" value="John Doe"/>
    </div>
    <div>
    <label class="mb-1.5 block text-sm font-medium text-slate-700" for="initials">Initials</label>
    <input class="form-input block w-full rounded-xl border-slate-300 bg-white p-3.5 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="initials" maxlength="3" placeholder="e.g., JD" type="text" value="JD"/>
    </div>
    <div>
    <label class="mb-1.5 block text-sm font-medium text-slate-700" for="role">Role</label>
    <select class="form-select block w-full rounded-xl border-slate-300 bg-white p-3.5 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="role">
    <option value="">Select your role</option>
    <option selected="" value="developer">Developer</option>
    <option value="designer">Designer</option>
    <option value="manager">Manager</option>
    <option value="other">Other</option>
    </select>
    </div>
    <div>
    <label class="mb-1.5 block text-sm font-medium text-slate-700" for="phone">Phone Number (Hong Kong)</label>
    <div class="relative">
    <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3.5">
    <span class="text-slate-500">+852</span>
    </div>
    <input class="form-input block w-full rounded-xl border-slate-300 bg-white p-3.5 pl-16 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="phone" placeholder="Enter your phone number" type="tel" value="12345678"/>
    </div>
    </div>
    <div>
    <p class="mb-2 text-sm font-medium text-slate-700">Profile Icon Color</p>
    <div class="grid grid-cols-6 gap-3">
    <label class="relative flex size-10 cursor-pointer items-center justify-center rounded-full border-2 border-transparent ring-2 ring-transparent transition-all duration-150 ease-in-out hover:opacity-80 has-[:checked]:border-white has-[:checked]:ring-blue-500" style="background-color: #FF6347;">
    <input class="peer sr-only" name="profile_color" type="radio" value="#FF6347"/>
    <span class="material-symbols-outlined hidden text-white peer-checked:block">check</span>
    </label>
    <label class="relative flex size-10 cursor-pointer items-center justify-center rounded-full border-2 border-transparent ring-2 ring-transparent transition-all duration-150 ease-in-out hover:opacity-80 has-[:checked]:border-white has-[:checked]:ring-blue-500" style="background-color: #4682B4;">
    <input checked="" class="peer sr-only" name="profile_color" type="radio" value="#4682B4"/>
    <span class="material-symbols-outlined hidden text-white peer-checked:block">check</span>
    </label>
    <label class="relative flex size-10 cursor-pointer items-center justify-center rounded-full border-2 border-transparent ring-2 ring-transparent transition-all duration-150 ease-in-out hover:opacity-80 has-[:checked]:border-white has-[:checked]:ring-blue-500" style="background-color: #32CD32;">
    <input class="peer sr-only" name="profile_color" type="radio" value="#32CD32"/>
    <span class="material-symbols-outlined hidden text-white peer-checked:block">check</span>
    </label>
    <label class="relative flex size-10 cursor-pointer items-center justify-center rounded-full border-2 border-transparent ring-2 ring-transparent transition-all duration-150 ease-in-out hover:opacity-80 has-[:checked]:border-white has-[:checked]:ring-blue-500" style="background-color: #FFD700;">
    <input class="peer sr-only" name="profile_color" type="radio" value="#FFD700"/>
    <span class="material-symbols-outlined hidden text-white peer-checked:block">check</span>
    </label>
    <label class="relative flex size-10 cursor-pointer items-center justify-center rounded-full border-2 border-transparent ring-2 ring-transparent transition-all duration-150 ease-in-out hover:opacity-80 has-[:checked]:border-white has-[:checked]:ring-blue-500" style="background-color: #DA70D6;">
    <input class="peer sr-only" name="profile_color" type="radio" value="#DA70D6"/>
    <span class="material-symbols-outlined hidden text-white peer-checked:block">check</span>
    </label>
    <label class="relative flex size-10 cursor-pointer items-center justify-center rounded-full border-2 border-transparent ring-2 ring-transparent transition-all duration-150 ease-in-out hover:opacity-80 has-[:checked]:border-white has-[:checked]:ring-blue-500" style="background-color: #87CEFA;">
    <input class="peer sr-only" name="profile_color" type="radio" value="#87CEFA"/>
    <span class="material-symbols-outlined hidden text-white peer-checked:block">check</span>
    </label>
    </div>
    </div>
    </div>
    </main>
    </div>
    <footer class="sticky bottom-0 bg-white px-4 py-3 shadow-[0_-2px_4px_rgba(0,0,0,0.05)]">
    <div class="mx-auto max-w-md">
    <button class="w-full rounded-xl bg-blue-600 px-5 py-3.5 text-base font-semibold text-white shadow-sm hover:bg-blue-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" type="submit">
                Save Changes
              </button>
    </div>
    </footer>
    </div>
    <script>
          const colorRadios = document.querySelectorAll('input[name="profile_color"]');
          const initialsInput = document.getElementById('initials');
          const userIconPreview = document.getElementById('user-icon-preview');
          function updateIcon() {
            const selectedColor = document.querySelector('input[name="profile_color"]:checked').value;
            const initials = initialsInput.value.toUpperCase();
            userIconPreview.style.backgroundColor = selectedColor;
            userIconPreview.textContent = initials;
            userIconPreview.style.color = isLight(selectedColor) ? '#333' : 'white';
          }
          function isLight(hexColor) {
            const hex = hexColor.replace('#', '');
            const r = parseInt(hex.substring(0, 2), 16);
            const g = parseInt(hex.substring(2, 4), 16);
            const b = parseInt(hex.substring(4, 6), 16);
            const brightness = (r * 299 + g * 587 + b * 114) / 1000;
            return brightness > 155;
          }
          colorRadios.forEach(radio => radio.addEventListener('change', updateIcon));
          initialsInput.addEventListener('input', updateIcon);
          // Initial call to set icon
          updateIcon();
        </script>
    
    </body></html>