<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Notification Details</title>
<script src="https://cdn.tailwindcss.com"></script>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style>
        body {
            font-family: 'Roboto', sans-serif;
        }
        .swipe-container {
            position: relative;
            overflow: hidden;
        }
        .swipe-content {
            transition: transform 0.3s ease-out;
        }
        .swipe-actions {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            display: flex;
            align-items: center;
            transform: translateX(100%);
            transition: transform 0.3s ease-out;
        }
        .swipe-container.swiped .swipe-content {
            transform: translateX(-80px);}
        .swipe-container.swiped .swipe-actions {
            transform: translateX(0);
        }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-100">
<div class="max-w-md mx-auto bg-white shadow-md">
<header class="bg-indigo-600 text-white p-4 flex items-center">
<i class="material-icons">arrow_back_ios</i>
<h1 class="text-xl font-semibold ml-2">Notification Details</h1>
</header>
<main class="p-6 space-y-6">
<section>
<h2 class="text-2xl font-bold text-gray-800">Mother/Baby Transfer</h2>
<p class="text-gray-600 mt-2">
<span class="font-semibold">Mother Initial:</span> Sophia Brown<br/>
<span class="font-semibold">Designated Ward:</span> D05
                </p>
<p class="text-sm text-gray-500">Yesterday, 9:03 PM</p>
</section>
<section class="bg-gray-50 p-4 rounded-lg shadow">
<h3 class="text-lg font-semibold text-gray-700">Clinical Notes:</h3>
<p class="text-gray-600 mt-2">
                    This is an important notification regarding the transfer of Sophia Brown and her baby girl. Please review the details and acknowledge receipt.
                </p>
</section>
<section>
<h3 class="text-lg font-semibold text-gray-700 mb-4">Sender &amp; Other Recipients</h3>
<div class="space-y-3">
<div class="bg-white p-4 rounded-lg border border-gray-200 flex items-center justify-between shadow-sm">
<div class="flex items-center space-x-3">
<img alt="Dr. Grace Hall avatar" class="w-10 h-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBnewFcf7Z1ViEI-DOVLtEqAJfV2pZs34Get9JUj6algN83TwvSjmOdsJbz9tnPCm5B8tBgaJtTL5fjw-j_n-j8SeKCqIVnAhqZSnq1Wrtd59C9ESprBbkZ3Ks34Pew-YLbnzQo_EydE_Da8Yo6VzWut-2LaaJ2SUhFB-nMC6f9i1I16JJtLm1noYbh6NUSgIDGHo5kzd5EAg1cBgy_kW7sxayCrL-0BDfA8tSuMgU3u4uMgm8ifrTwtwlpkCXmWSg4gic4mhZpwvE"/>
<div>
<p class="font-semibold text-gray-800">Grace Hall (Sender)</p>
</div>
</div>
<i class="material-icons text-gray-400">chevron_right</i>
</div>
<div class="bg-white p-4 rounded-lg border border-gray-200 flex items-center justify-between shadow-sm">
<div class="flex items-center space-x-3">
<div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
<i class="material-icons text-gray-500">person</i>
</div>
<div>
<p class="font-semibold text-gray-800">Nurse Ian Johns</p>
</div>
</div>
<div class="flex items-center space-x-2">
<span class="px-3 py-1 text-xs font-semibold text-green-700 bg-green-100 rounded-full flex items-center">
<i class="material-icons text-sm mr-1">check_circle</i>Confirmed
                            </span>
<i class="material-icons text-gray-400">chevron_right</i>
</div>
</div>

<div class="swipe-container rounded-lg border border-gray-200 shadow-sm">
<div class="swipe-actions">
<button class="bg-blue-500 text-white h-full px-6 flex items-center justify-center">
<i class="material-icons">call</i>
</button>
</div>
<div class="swipe-content bg-white p-4 flex items-center justify-between">
<div class="flex items-center space-x-3">
<img alt="Nurse Betty Early avatar" class="w-10 h-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBChJV-xTPWOvyUt46X6mpENnob0sPNvXtcbXkfHD2NAEvwZ7xeBM7L0jskltmKPbnND_8ELJLcfZ2I4r4vMGbZgIJgtwtkfzuNUT5nDhBO4aHEzA8_YOkHj31VJMeJAPrNUYKLQ9r0q-JR_8McH6-Ko_4M_-wXjHY6WWufNmiain7SDFoFKhOMClkhero7EbeZQm9SoRtNb4RnV8Vm1ea2dqfOx6FrNBOxBeDBv9AsDF42lQ6XvElpVSQpkO6f0s7mwLSqd6XIBag"/>
<div>
<p class="font-semibold text-gray-800">You (Nurse Betty Early)</p>
</div>
</div>
<div class="flex items-center space-x-2">
<span class="px-3 py-1 text-xs font-semibold text-orange-700 bg-orange-100 rounded-full flex items-center">
<i class="material-icons text-sm mr-1">hourglass_empty</i>Received
                                </span>
<i class="material-icons text-gray-400">chevron_right</i>
</div>
</div>
</div>
<div class="swipe-container rounded-lg border border-gray-200 shadow-sm">
<div class="swipe-actions">
<button class="bg-blue-500 text-white h-full px-6 flex items-center justify-center">
<i class="material-icons">call</i>
</button>
</div>
<div class="swipe-content bg-white p-4 flex items-center justify-between">
<div class="flex items-center space-x-3">
<div class="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-white">
<span class="text-lg font-semibold">PC</span>
</div>
<div>
<p class="font-semibold text-gray-800">Pending Confirmation</p>
</div>
</div>
<i class="material-icons text-gray-400">chevron_right</i>
</div>
</div>
</div>
</section>
</main>
<footer class="p-4 sticky bottom-0 bg-white border-t border-gray-200">
<button class="w-full bg-teal-500 hover:bg-teal-600 text-white font-semibold py-3 px-4 rounded-lg flex items-center justify-center text-lg">
<i class="material-icons mr-2">check_circle_outline</i>
                Acknowledge
            </button>
</footer>
</div>
<script>
        document.querySelectorAll('.swipe-container').forEach(container => {
            let startX;
            let isSwiping = false;
            const content = container.querySelector('.swipe-content');
            // Check if this is the "Pending Confirmation" item
            const isPendingConfirmation = container.querySelector('.font-semibold.text-gray-800')?.textContent.includes('Pending Confirmation');
            if (isPendingConfirmation) {
                content.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    isSwiping = true;
                });
                content.addEventListener('touchmove', (e) => {
                    if (!isSwiping) return;
                    const currentX = e.touches[0].clientX;
                    const diffX = currentX - startX;
                    // Only allow swiping left
                    if (diffX < 0 && diffX > -100) { // Limit swipe distance
                        content.style.transform = `translateX(${diffX}px)`;
                    }
                });
                content.addEventListener('touchend', (e) => {
                    if (!isSwiping) return;
                    isSwiping = false;
                    const currentX = e.changedTouches[0].clientX;
                    const diffX = currentX - startX;
                    if (diffX < -40) { // Threshold to trigger swipe
                        container.classList.add('swiped');
                    } else {
                        container.classList.remove('swiped');
                        content.style.transform = 'translateX(0)';
                    }
                });
                // Prevent click event propagation if swiped
                const callButton = container.querySelector('.swipe-actions button');
                if (callButton) {
                    callButton.addEventListener('click', (e) => {
                        if (container.classList.contains('swiped')) {
                             // Add your call functionality here
                            alert('Calling...');
                        }
                    });
                }
            }
        });
    </script>

</body></html>